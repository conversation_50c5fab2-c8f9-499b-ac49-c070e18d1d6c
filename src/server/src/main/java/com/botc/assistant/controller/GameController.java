package com.botc.assistant.controller;

import com.botc.assistant.model.Game;
import com.botc.assistant.model.Player;
import com.botc.assistant.service.GameService;
import com.botc.assistant.service.RoleService;
import com.botc.assistant.repository.PlayerRepository;
import com.botc.assistant.roles.Role;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.Optional;

/**
 * REST controller for game management operations.
 */
@RestController
@RequestMapping("/api/games")
@CrossOrigin(origins = { "http://localhost:5173", "http://localhost:5174", "http://localhost:3000" })
public class GameController {

    @Autowired
    private GameService gameService;

    @Autowired
    private RoleService roleService;

    @Autowired
    private PlayerRepository playerRepository;

    /**
     * Create a new game.
     */
    @PostMapping
    public ResponseEntity<Game> createGame(@RequestBody CreateGameRequest request) {
        try {
            System.out.println("Creating game with request: " + request.getName() + ", maxPlayers: "
                    + request.getMaxPlayers() + ", storytellerId: " + request.getStorytellerId());
            Game game = gameService.createGame(
                    request.getName(),
                    request.getMaxPlayers(),
                    request.getStorytellerId());
            return ResponseEntity.ok(game);
        } catch (Exception e) {
            System.err.println("Error creating game: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Get all active games.
     */
    @GetMapping
    public ResponseEntity<List<Game>> getAllGames() {
        try {
            List<Game> games = gameService.getAllActiveGames();
            return ResponseEntity.ok(games);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Get a game by ID.
     */
    @GetMapping("/{gameId}")
    public ResponseEntity<Game> getGame(@PathVariable String gameId) {
        try {
            return gameService.findById(gameId)
                    .map(ResponseEntity::ok)
                    .orElse(ResponseEntity.notFound().build());
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Get a game by join code.
     */
    @GetMapping("/code/{code}")
    public ResponseEntity<Game> getGameByCode(@PathVariable String code) {
        try {
            return gameService.findByCode(code)
                    .map(ResponseEntity::ok)
                    .orElse(ResponseEntity.notFound().build());
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Join a game.
     */
    @PostMapping("/{gameId}/join")
    public ResponseEntity<Player> joinGame(@PathVariable String gameId,
            @RequestBody JoinGameRequest request) {
        try {
            System.out.println("Attempting to join game: " + gameId + " with user: " + request.getUserId());
            Player player = gameService.addPlayer(gameId, request.getUserId());
            System.out.println("Successfully joined game. Player: " + player.getName());
            return ResponseEntity.ok(player);
        } catch (Exception e) {
            System.err.println("Error joining game: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Leave a game.
     */
    @PostMapping("/{gameId}/leave")
    public ResponseEntity<Void> leaveGame(@PathVariable String gameId,
            @RequestBody Map<String, String> request) {
        try {
            String playerId = request.get("playerId");
            gameService.removePlayer(gameId, playerId);
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Start a game.
     */
    @PostMapping("/{gameId}/start")
    public ResponseEntity<Game> startGame(@PathVariable String gameId,
            @RequestBody Map<String, String> request) {
        try {
            String storytellerId = request.get("storytellerId");
            Game game = gameService.startGame(gameId, storytellerId);
            return ResponseEntity.ok(game);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * End a game.
     */
    @PostMapping("/{gameId}/end")
    public ResponseEntity<Game> endGame(@PathVariable String gameId,
            @RequestBody Map<String, String> request) {
        try {
            String storytellerId = request.get("storytellerId");
            Game game = gameService.endGame(gameId, storytellerId);
            return ResponseEntity.ok(game);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Delete a game.
     */
    @DeleteMapping("/{gameId}")
    public ResponseEntity<Void> deleteGame(@PathVariable String gameId,
            @RequestParam String storytellerId) {
        try {
            gameService.deleteGame(gameId, storytellerId);
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Update game phase.
     */
    @PostMapping("/{gameId}/phase")
    public ResponseEntity<Game> updatePhase(@PathVariable String gameId,
            @RequestBody UpdatePhaseRequest request) {
        try {
            Game.GamePhase phase = Game.GamePhase.valueOf(request.getPhase());
            Game game = gameService.updatePhase(gameId, phase, request.getStorytellerId());
            return ResponseEntity.ok(game);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Get games by storyteller.
     */
    @GetMapping("/storyteller/{storytellerId}")
    public ResponseEntity<List<Game>> getGamesByStoryteller(@PathVariable String storytellerId) {
        try {
            List<Game> games = gameService.getGamesByStoryteller(storytellerId);
            return ResponseEntity.ok(games);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Assign roles to players in a game.
     */
    @PostMapping("/{gameId}/assign-roles")
    public ResponseEntity<Map<String, Object>> assignRoles(@PathVariable String gameId) {
        try {
            Optional<Game> gameOpt = gameService.findById(gameId);
            if (gameOpt.isEmpty()) {
                return ResponseEntity.notFound().build();
            }

            Game game = gameOpt.get();
            List<Player> players = playerRepository.findByGameId(gameId);

            if (players.isEmpty()) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "No players found in game");
                return ResponseEntity.badRequest().body(response);
            }

            // Simple role assignment for demo - assign random roles
            List<Role> availableRoles = roleService.getAllRoles();
            if (availableRoles.isEmpty()) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "No roles available");
                return ResponseEntity.badRequest().body(response);
            }

            // Assign roles randomly for demo purposes
            java.util.Random random = new java.util.Random();
            int rolesAssigned = 0;

            for (Player player : players) {
                if (rolesAssigned < availableRoles.size()) {
                    Role assignedRole = availableRoles.get(random.nextInt(availableRoles.size()));
                    // In a real implementation, you'd update the player's role in the database
                    // For now, we'll just count successful assignments
                    rolesAssigned++;
                }
            }

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Roles assigned successfully");
            response.put("playersCount", players.size());
            response.put("rolesAssigned", rolesAssigned);

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Error assigning roles: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    // Request DTOs
    public static class CreateGameRequest {
        private String name;
        private int maxPlayers = 20;
        private String storytellerId;

        // Getters and setters
        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getMaxPlayers() {
            return maxPlayers;
        }

        public void setMaxPlayers(int maxPlayers) {
            this.maxPlayers = maxPlayers;
        }

        public String getStorytellerId() {
            return storytellerId;
        }

        public void setStorytellerId(String storytellerId) {
            this.storytellerId = storytellerId;
        }
    }

    public static class JoinGameRequest {
        private String userId;

        // Getters and setters
        public String getUserId() {
            return userId;
        }

        public void setUserId(String userId) {
            this.userId = userId;
        }
    }

    public static class UpdatePhaseRequest {
        private String phase;
        private String storytellerId;

        // Getters and setters
        public String getPhase() {
            return phase;
        }

        public void setPhase(String phase) {
            this.phase = phase;
        }

        public String getStorytellerId() {
            return storytellerId;
        }

        public void setStorytellerId(String storytellerId) {
            this.storytellerId = storytellerId;
        }
    }
}
