package com.botc.assistant.roles.outsiders;

import com.botc.assistant.roles.AbstractRole;
import com.botc.assistant.roles.RoleTeam;
import com.botc.assistant.roles.RoleType;

/**
 * Abstract base class for all Outsider roles.
 * Outsiders are good team members with harmful or limited abilities.
 */
public abstract class Outsider extends AbstractRole {
    
    protected Outsider(Builder<?> builder) {
        super(builder);
    }
    
    /**
     * Builder for Outsider roles
     */
    public abstract static class Builder<T extends Builder<T>> extends AbstractRole.Builder<T> {
        
        public Builder() {
            // Set default values for all Outsiders
            team(RoleTeam.GOOD);
            type(RoleType.OUTSIDER);
            canBeTargeted(true);
        }
        
        @Override
        public abstract Outsider build();
    }
}
