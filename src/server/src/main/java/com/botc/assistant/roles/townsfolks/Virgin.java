package com.botc.assistant.roles.townsfolks;

/**
 * Virgin - Townsfolk role
 * 
 * The 1st time you are nominated, if the nominator is a Townsfolk, they are executed immediately.
 * 
 * The Virgin has a powerful defensive ability that triggers on nomination.
 */
public class Virgin extends TownsFolk {
    
    public static final String ROLE_ID = "virgin";
    public static final String ROLE_NAME = "Virgin";
    public static final String ROLE_DESCRIPTION = "The 1st time you are nominated, if the nominator is a Townsfolk, they are executed immediately.";
    
    private Virgin(Builder builder) {
        super(builder);
    }
    
    public static Builder builder() {
        return new Builder();
    }
    
    public static Virgin create() {
        return builder().build();
    }
    
    /**
     * Builder for Virgin role
     */
    public static class Builder extends TownsFolk.Builder<Builder> {
        
        public Builder() {
            id(ROLE_ID);
            name(ROLE_NAME);
            description(ROLE_DESCRIPTION);
            setupInfo("First nomination triggers execution of nominator if they are Townsfolk");
        }
        
        @Override
        protected Builder self() {
            return this;
        }
        
        @Override
        public Virgin build() {
            return new Virgin(this);
        }
    }
}
