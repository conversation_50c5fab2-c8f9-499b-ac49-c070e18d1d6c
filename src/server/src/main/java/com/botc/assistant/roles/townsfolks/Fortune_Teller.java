package com.botc.assistant.roles.townsfolks;

/**
 * Fortune Teller - Townsfolk role
 * 
 * Each night, choose 2 players: you learn if either is a Demon. There is a good player that registers as a Demon to you.
 * 
 * The Fortune Teller can detect demons but has a red herring that always registers as evil.
 */
public class Fortune_Teller extends TownsFolk {
    
    public static final String ROLE_ID = "fortune_teller";
    public static final String ROLE_NAME = "Fortune Teller";
    public static final String ROLE_DESCRIPTION = "Each night, choose 2 players: you learn if either is a Demon. There is a good player that registers as a Demon to you.";
    
    private Fortune_Teller(Builder builder) {
        super(builder);
    }
    
    public static Builder builder() {
        return new Builder();
    }
    
    public static Fortune_Teller create() {
        return builder().build();
    }
    
    /**
     * Builder for Fortune Teller role
     */
    public static class Builder extends TownsFolk.Builder<Builder> {
        
        public Builder() {
            id(ROLE_ID);
            name(ROLE_NAME);
            description(ROLE_DESCRIPTION);
            hasNightAbility(true);
            nightPriority(15); // Each night information
            setupInfo("Has a red herring - a good player that registers as a Demon");
        }
        
        @Override
        protected Builder self() {
            return this;
        }
        
        @Override
        public Fortune_Teller build() {
            return new Fortune_Teller(this);
        }
    }
}
