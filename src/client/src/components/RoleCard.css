.role-card {
  width: 200px;
  height: 280px;
  perspective: 1000px;
  cursor: pointer;
  margin: 8px;
  transition: transform 0.2s ease;
}

.role-card:hover {
  transform: translateY(-4px);
}

.role-card.empty {
  cursor: default;
}

.role-card.empty:hover {
  transform: none;
}

.card-inner {
  position: relative;
  width: 100%;
  height: 100%;
  text-align: center;
  transition: transform 0.6s;
  transform-style: preserve-3d;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.role-card.flipped .card-inner {
  transform: rotateY(180deg);
}

.card-front, .card-back {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  border-radius: 12px;
  padding: 16px;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

.card-front {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 2px solid var(--team-color, #95a5a6);
}

.card-back {
  background: linear-gradient(135deg, #343a40 0%, #495057 100%);
  color: white;
  transform: rotateY(180deg);
  border: 2px solid var(--team-color, #95a5a6);
}

.role-card.good .card-front {
  border-color: #2ecc71;
  background: linear-gradient(135deg, #d5f4e6 0%, #a8e6cf 100%);
}

.role-card.evil .card-front {
  border-color: #e74c3c;
  background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  height: 24px;
}

.role-type-icon {
  font-size: 1.5rem;
}

.team-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.details-btn, .close-btn {
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.details-btn:hover, .close-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.role-name {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0 0 8px 0;
  text-align: center;
}

.role-type {
  font-size: 0.9rem;
  color: #666;
  margin: 0 0 12px 0;
  text-transform: capitalize;
}

.card-back .role-type {
  color: #ccc;
}

.role-description {
  font-size: 0.85rem;
  line-height: 1.4;
  margin: 0 0 12px 0;
  text-align: left;
}

.setup-info, .night-priority {
  font-size: 0.75rem;
  margin: 8px 0;
  text-align: left;
}

.ability-indicator {
  position: absolute;
  bottom: 16px;
  font-size: 1.2rem;
}

.ability-indicator.night {
  right: 16px;
}

.ability-indicator.day {
  left: 16px;
}

.role-placeholder {
  font-size: 4rem;
  color: #ccc;
  margin-bottom: 16px;
}

.empty .role-name {
  color: #999;
}

/* Details overlay */
.details-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.details-content {
  background: white;
  border-radius: 12px;
  max-width: 500px;
  max-height: 80vh;
  overflow-y: auto;
  margin: 20px;
}

.details-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 20px 0 20px;
  border-bottom: 1px solid #eee;
}

.details-header h2 {
  margin: 0;
  color: #333;
}

.details-body {
  padding: 20px;
}

.detail-section {
  margin-bottom: 20px;
}

.detail-section h4 {
  margin: 0 0 8px 0;
  color: #555;
  font-size: 1rem;
}

.detail-section p, .detail-section ul {
  margin: 0;
  color: #666;
  line-height: 1.5;
}

.detail-section ul {
  padding-left: 20px;
}
