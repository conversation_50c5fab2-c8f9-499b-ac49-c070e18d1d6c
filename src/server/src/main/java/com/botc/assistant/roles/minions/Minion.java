package com.botc.assistant.roles.minions;

import com.botc.assistant.roles.AbstractRole;
import com.botc.assistant.roles.RoleTeam;
import com.botc.assistant.roles.RoleType;

/**
 * Abstract base class for all Minion roles.
 * Minions are evil team members who support the demon.
 */
public abstract class Minion extends AbstractRole {
    
    protected Minion(Builder<?> builder) {
        super(builder);
    }
    
    /**
     * Builder for Minion roles
     */
    public abstract static class Builder<T extends Builder<T>> extends AbstractRole.Builder<T> {
        
        public Builder() {
            // Set default values for all Minions
            team(RoleTeam.EVIL);
            type(RoleType.MINION);
            canBeTargeted(true);
        }
        
        @Override
        public abstract Minion build();
    }
}
