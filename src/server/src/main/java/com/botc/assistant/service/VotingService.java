package com.botc.assistant.service;

import com.botc.assistant.model.Game;
import com.botc.assistant.model.Player;
import com.botc.assistant.model.Vote;
import com.botc.assistant.repository.VoteRepository;
import com.botc.assistant.repository.PlayerRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * Service for managing voting sessions in Blood on the Clock Tower games.
 * Handles nominations, voting, and vote resolution.
 */
@Service
public class VotingService {

    private final VoteRepository voteRepository;
    private final PlayerRepository playerRepository;

    @Autowired
    public VotingService(VoteRepository voteRepository, PlayerRepository playerRepository) {
        this.voteRepository = voteRepository;
        this.playerRepository = playerRepository;
    }

    /**
     * Nominate a player for execution.
     */
    public Vote nominatePlayer(Game game, Player nominator, Player nominee) {
        if (game.getCurrentPhase() != Game.GamePhase.DAY && game.getCurrentPhase() != Game.GamePhase.VOTING) {
            throw new IllegalStateException("Nominations can only happen during day phase");
        }

        if (!nominator.isAlive()) {
            throw new IllegalStateException("Dead players cannot nominate");
        }

        if (!nominee.isAlive()) {
            throw new IllegalStateException("Cannot nominate dead players");
        }

        if (nominator.getId().equals(nominee.getId())) {
            throw new IllegalStateException("Players cannot nominate themselves");
        }

        // Check if there's already an active vote
        Optional<Vote> activeVote = voteRepository.findCurrentActiveVote(game.getId());
        if (activeVote.isPresent()) {
            throw new IllegalStateException("There is already an active vote");
        }

        // Create new vote
        Vote vote = new Vote(nominator, nominee, game);
        vote = voteRepository.save(vote);

        // Mark nominee as nominated
        nominee.nominate();
        playerRepository.save(nominee);

        return vote;
    }

    /**
     * Cast a vote in the current voting session.
     */
    public void castVote(Game game, Player player, Vote.VoteChoice choice) {
        if (!player.canVote()) {
            throw new IllegalStateException("Player cannot vote");
        }

        Optional<Vote> activeVoteOpt = voteRepository.findCurrentActiveVote(game.getId());
        if (activeVoteOpt.isEmpty()) {
            throw new IllegalStateException("No active vote to cast");
        }

        Vote vote = activeVoteOpt.get();
        vote.castVote(player.getId(), choice);
        voteRepository.save(vote);

        // Mark player as having voted
        player.vote();
        playerRepository.save(player);
    }

    /**
     * Start a voting session (storyteller action).
     */
    public void startVoting(Game game) {
        if (game.getCurrentPhase() != Game.GamePhase.DAY) {
            throw new IllegalStateException("Voting can only start during day phase");
        }

        // Reset all players' voting status
        List<Player> players = playerRepository.findByGameId(game.getId());
        for (Player player : players) {
            player.resetVote();
            playerRepository.save(player);
        }
    }

    /**
     * End the current voting session.
     */
    public Vote endVoting(Game game) {
        Optional<Vote> activeVoteOpt = voteRepository.findCurrentActiveVote(game.getId());
        if (activeVoteOpt.isEmpty()) {
            throw new IllegalStateException("No active vote to end");
        }

        Vote vote = activeVoteOpt.get();
        vote.completeVote();
        voteRepository.save(vote);

        // Clear nomination status
        if (vote.getNomineeId() != null) {
            Optional<Player> nomineeOpt = playerRepository.findById(vote.getNomineeId());
            if (nomineeOpt.isPresent()) {
                Player nominee = nomineeOpt.get();
                nominee.clearNomination();
                
                // If vote passed, execute the player
                if (vote.getResult() == Vote.VoteResult.PASSED) {
                    nominee.kill();
                }
                
                playerRepository.save(nominee);
            }
        }

        // Reset all players' voting status
        List<Player> players = playerRepository.findByGameId(game.getId());
        for (Player player : players) {
            player.resetVote();
            playerRepository.save(player);
        }

        return vote;
    }

    /**
     * Get the current active vote for a game.
     */
    public Optional<Vote> getCurrentVote(String gameId) {
        return voteRepository.findCurrentActiveVote(gameId);
    }

    /**
     * Get all votes for a game.
     */
    public List<Vote> getGameVotes(String gameId) {
        return voteRepository.findByGameId(gameId);
    }

    /**
     * Cancel the current vote (storyteller action).
     */
    public void cancelCurrentVote(Game game) {
        Optional<Vote> activeVoteOpt = voteRepository.findCurrentActiveVote(game.getId());
        if (activeVoteOpt.isEmpty()) {
            throw new IllegalStateException("No active vote to cancel");
        }

        Vote vote = activeVoteOpt.get();
        vote.setActive(false);
        vote.setResult(Vote.VoteResult.FAILED);
        voteRepository.save(vote);

        // Clear nomination status
        if (vote.getNomineeId() != null) {
            Optional<Player> nomineeOpt = playerRepository.findById(vote.getNomineeId());
            if (nomineeOpt.isPresent()) {
                Player nominee = nomineeOpt.get();
                nominee.clearNomination();
                playerRepository.save(nominee);
            }
        }

        // Reset all players' voting status
        List<Player> players = playerRepository.findByGameId(game.getId());
        for (Player player : players) {
            player.resetVote();
            playerRepository.save(player);
        }
    }

    /**
     * Check if a player has voted in the current vote.
     */
    public boolean hasPlayerVoted(String gameId, String playerId) {
        Optional<Vote> activeVote = voteRepository.findCurrentActiveVote(gameId);
        return activeVote.map(vote -> vote.hasPlayerVoted(playerId)).orElse(false);
    }

    /**
     * Get vote results for a completed vote.
     */
    public Optional<Vote> getVoteResults(String voteId) {
        return voteRepository.findById(voteId);
    }
}
