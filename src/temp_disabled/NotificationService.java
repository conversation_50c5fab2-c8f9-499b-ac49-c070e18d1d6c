package com.botc.assistant.service;

import com.botc.assistant.model.Game;
import com.botc.assistant.model.GamePhase;
import com.botc.assistant.model.Player;
import com.botc.assistant.model.Vote;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * Service for sending real-time notifications via WebSocket.
 * Handles all game events and broadcasts them to connected clients.
 */
@Service
public class NotificationService {

    private final SimpMessagingTemplate messagingTemplate;

    @Autowired
    public NotificationService(SimpMessagingTemplate messagingTemplate) {
        this.messagingTemplate = messagingTemplate;
    }

    /**
     * Notify all players in a game of a phase change.
     */
    public void notifyPhaseChange(Game game, GamePhase newPhase) {
        Map<String, Object> payload = new HashMap<>();
        payload.put("type", "phase.changed");
        payload.put("gameId", game.getId());
        payload.put("phase", newPhase.getType().name());
        payload.put("dayNumber", newPhase.getDayNumber());
        payload.put("timestamp", LocalDateTime.now());

        broadcastToGame(game.getId(), payload);
    }

    /**
     * Notify players that a new player has joined the game.
     */
    public void notifyPlayerJoined(Game game, Player player) {
        Map<String, Object> payload = new HashMap<>();
        payload.put("type", "game.player.joined");
        payload.put("gameId", game.getId());
        payload.put("playerId", player.getId());
        payload.put("playerName", player.getName());
        payload.put("position", player.getPosition());
        payload.put("currentPlayers", game.getCurrentPlayers());
        payload.put("timestamp", LocalDateTime.now());

        broadcastToGame(game.getId(), payload);
    }

    /**
     * Notify players that a player has left the game.
     */
    public void notifyPlayerLeft(Game game, Player player) {
        Map<String, Object> payload = new HashMap<>();
        payload.put("type", "game.player.left");
        payload.put("gameId", game.getId());
        payload.put("playerId", player.getId());
        payload.put("playerName", player.getName());
        payload.put("currentPlayers", game.getCurrentPlayers());
        payload.put("timestamp", LocalDateTime.now());

        broadcastToGame(game.getId(), payload);
    }

    /**
     * Notify players that the game has started.
     */
    public void notifyGameStarted(Game game) {
        Map<String, Object> payload = new HashMap<>();
        payload.put("type", "game.started");
        payload.put("gameId", game.getId());
        payload.put("timestamp", LocalDateTime.now());

        broadcastToGame(game.getId(), payload);
    }

    /**
     * Notify players that the game has ended.
     */
    public void notifyGameEnd(Game game, Game.GameState result) {
        Map<String, Object> payload = new HashMap<>();
        payload.put("type", "game.ended");
        payload.put("gameId", game.getId());
        payload.put("result", result.name());
        payload.put("timestamp", LocalDateTime.now());

        broadcastToGame(game.getId(), payload);
    }

    /**
     * Notify players of a new nomination.
     */
    public void notifyNomination(Game game, Vote vote) {
        Map<String, Object> payload = new HashMap<>();
        payload.put("type", "nomination.created");
        payload.put("gameId", game.getId());
        payload.put("voteId", vote.getId());
        payload.put("nominatorName", vote.getNominator().getName());
        payload.put("nomineeName", vote.getNominee().getName());
        payload.put("timestamp", LocalDateTime.now());

        broadcastToGame(game.getId(), payload);
    }

    /**
     * Notify players that voting has started.
     */
    public void notifyVotingStarted(Game game, Vote vote) {
        Map<String, Object> payload = new HashMap<>();
        payload.put("type", "vote.started");
        payload.put("gameId", game.getId());
        payload.put("voteId", vote.getId());
        payload.put("nominatorName", vote.getNominator().getName());
        payload.put("nomineeName", vote.getNominee().getName());
        payload.put("timestamp", LocalDateTime.now());

        broadcastToGame(game.getId(), payload);
    }

    /**
     * Notify players that a vote has been cast.
     */
    public void notifyVoteCast(Game game, Player voter, Vote.VoteChoice choice) {
        Map<String, Object> payload = new HashMap<>();
        payload.put("type", "vote.cast");
        payload.put("gameId", game.getId());
        payload.put("voterName", voter.getName());
        payload.put("choice", choice.name());
        payload.put("timestamp", LocalDateTime.now());

        broadcastToGame(game.getId(), payload);
    }

    /**
     * Notify players that voting has ended.
     */
    public void notifyVotingEnded(Game game, Vote vote, Vote.VoteResult result) {
        Map<String, Object> payload = new HashMap<>();
        payload.put("type", "vote.completed");
        payload.put("gameId", game.getId());
        payload.put("voteId", vote.getId());
        payload.put("result", result.name());
        payload.put("votesFor", vote.getVotesFor());
        payload.put("votesAgainst", vote.getVotesAgainst());
        payload.put("abstentions", vote.getAbstentions());
        payload.put("nomineeName", vote.getNominee().getName());
        payload.put("timestamp", LocalDateTime.now());

        broadcastToGame(game.getId(), payload);
    }

    /**
     * Notify players that a vote has been cancelled.
     */
    public void notifyVoteCancelled(Game game, Vote vote) {
        Map<String, Object> payload = new HashMap<>();
        payload.put("type", "vote.cancelled");
        payload.put("gameId", game.getId());
        payload.put("voteId", vote.getId());
        payload.put("timestamp", LocalDateTime.now());

        broadcastToGame(game.getId(), payload);
    }

    /**
     * Notify players that a player has died.
     */
    public void notifyPlayerDied(Game game, Player player, String cause) {
        Map<String, Object> payload = new HashMap<>();
        payload.put("type", "player.died");
        payload.put("gameId", game.getId());
        payload.put("playerId", player.getId());
        payload.put("playerName", player.getName());
        payload.put("cause", cause);
        payload.put("timestamp", LocalDateTime.now());

        broadcastToGame(game.getId(), payload);
    }

    /**
     * Notify players that roles have been assigned.
     */
    public void notifyRolesAssigned(Game game) {
        Map<String, Object> payload = new HashMap<>();
        payload.put("type", "roles.assigned");
        payload.put("gameId", game.getId());
        payload.put("timestamp", LocalDateTime.now());

        broadcastToGame(game.getId(), payload);
        
        // Send individual role assignments to each player
        game.getPlayers().forEach(player -> {
            if (player.getRole() != null) {
                notifyRoleAssignment(player);
            }
        });
    }

    /**
     * Notify a specific player of their role assignment.
     */
    public void notifyRoleAssignment(Player player) {
        Map<String, Object> payload = new HashMap<>();
        payload.put("type", "role.assigned");
        payload.put("playerId", player.getId());
        payload.put("roleId", player.getRole().getId());
        payload.put("roleName", player.getRole().getName());
        payload.put("roleType", player.getRole().getType().name());
        payload.put("roleTeam", player.getRole().getTeam().name());
        payload.put("description", player.getRole().getDescription());
        payload.put("timestamp", LocalDateTime.now());

        sendToPlayer(player.getId(), payload);
    }

    /**
     * Notify that a role action is requested.
     */
    public void notifyRoleAction(Game game, String roleId) {
        Map<String, Object> payload = new HashMap<>();
        payload.put("type", "role.action.requested");
        payload.put("gameId", game.getId());
        payload.put("roleId", roleId);
        payload.put("timestamp", LocalDateTime.now());

        // Send to storyteller
        sendToStoryteller(game.getStoryteller().getId(), payload);
        
        // Send to player with this role
        game.getPlayers().stream()
                .filter(p -> p.getRoleId() != null && p.getRoleId().equals(roleId))
                .findFirst()
                .ifPresent(player -> sendToPlayer(player.getId(), payload));
    }

    /**
     * Notify that night phase is complete.
     */
    public void notifyNightPhaseComplete(Game game) {
        Map<String, Object> payload = new HashMap<>();
        payload.put("type", "phase.night.completed");
        payload.put("gameId", game.getId());
        payload.put("timestamp", LocalDateTime.now());

        broadcastToGame(game.getId(), payload);
    }

    /**
     * Notify that phase has been paused.
     */
    public void notifyPhasePaused(Game game) {
        Map<String, Object> payload = new HashMap<>();
        payload.put("type", "phase.paused");
        payload.put("gameId", game.getId());
        payload.put("timestamp", LocalDateTime.now());

        broadcastToGame(game.getId(), payload);
    }

    /**
     * Notify that phase has been resumed.
     */
    public void notifyPhaseResumed(Game game) {
        Map<String, Object> payload = new HashMap<>();
        payload.put("type", "phase.resumed");
        payload.put("gameId", game.getId());
        payload.put("timestamp", LocalDateTime.now());

        broadcastToGame(game.getId(), payload);
    }

    /**
     * Send a custom message to all players in a game.
     */
    public void sendCustomMessage(String gameId, String message, String type) {
        Map<String, Object> payload = new HashMap<>();
        payload.put("type", type);
        payload.put("gameId", gameId);
        payload.put("message", message);
        payload.put("timestamp", LocalDateTime.now());

        broadcastToGame(gameId, payload);
    }

    // Private helper methods

    private void broadcastToGame(String gameId, Map<String, Object> payload) {
        messagingTemplate.convertAndSend("/topic/game/" + gameId, payload);
    }

    private void sendToPlayer(String playerId, Map<String, Object> payload) {
        messagingTemplate.convertAndSendToUser(playerId, "/queue/player", payload);
    }

    private void sendToStoryteller(String storytellerId, Map<String, Object> payload) {
        messagingTemplate.convertAndSendToUser(storytellerId, "/queue/storyteller", payload);
    }
}
