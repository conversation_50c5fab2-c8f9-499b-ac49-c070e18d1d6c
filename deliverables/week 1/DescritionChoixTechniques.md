# Description des choix techniques

Berberat Alex  
<PERSON>rat <PERSON>

## Backend
- `Spring Boot 3.3.0` - Java framework for building production-ready applications
- `<PERSON>ven` - Dependency management and build tool
- `Java 17` - Long-term support Java version

## Frontend
- `React 18` - Modern JavaScript library for building user interfaces
- `Vite` - Fast build tool and development server
- `CSS3` - Modern styling with gradients, animations, and responsive design
- `ES6+` - Modern JavaScript features

## Testing
- `Junit 5` – Java testing framework for backend unit testing, integrated with Spring Boot
- `Github Actions` – CI workflow that runs tests on every push and pull request

## Deployment
- `Docker` - Containerization for consistent deployments
- `GitHub Actions` - CI/CD pipeline automation
