# Description du projet de PDG - BloodOnTheClockTower-Assistant

<PERSON><PERSON>at <PERSON>

## Objectifs
Virtualiser le jeu de société [**Blood of the ClockTower**](https://bloodontheclocktower.com/).  
Le but est de renoncer aux propriétés physiques (cartes, jetons, etc) du jeu initial et de permettre au maître du jeu (MJ) d'avoir une vue d'ensemble de la partie (qui est vivant/mort, les effets spéciaux de certains rôles etc).

## Requirements Fonctionnels
L'application doit s'assurer du bon fonctionnement d'une partie du jeu **Blood on the ClockTower**.

L'application doit présenter deux options initialement: 
1) Rejoindre une partie
2) Créer une partie

L'option numéro 1 permet à un joueur de rejoindre une partie grâce à un code (id de la partie) et de jouer normalement.

Les joueurs doivent pouvoir prendre connaissance de leur rôle, ainsi qu'appliquer leur rôle, via leur téléphone joueur.

L'option numéro 2 permet de créer une partie par le Maître de Jeu (MJ) à l'issue d'un processus d'enregistrement (login).

Le Maître de Jeu doit pouvoir définir le nombre de joueurs pour la partie et choisir les rôles voulus (dans la limite des quotas existants du jeu)

Le Maître du Jeu doit pouvoir manager la partie et avoir accès à toutes les informations en temps réel. Il induit tout les changements d'état du jeu (nuit/jour/vote/etc) et permet aux joueurs d'appliquer leur rôle en temps et en heure.

## Requirements non-fonctionnels
L'application doit être facile d'utilisation et intuitive.

Le Maître de Jeu doit s'authentifier via un système sécurisé (login + mot de passe).

Le code de la partie (pour rejoindre) doit être suffisamment aléatoire pour éviter les collisions ou les tentatives de brute-force.

Le système doit afficher les changements d’état du jeu (jour/nuit, morts, votes, etc.) de manière visuelle et intuitive.

L'application doit pouvoir supporter au maximum 30 joueurs connectés simultanément à une partie sans ralentissement.

Le système doit permettre d’héberger plusieurs parties en parallèle sans conflit.


