package com.botc.assistant.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

/**
 * Represents a Blood on the Clock Tower game.
 * Stores game state, players, and configuration.
 */
public class Game {

    private String id;

    @NotBlank
    @Size(min = 3, max = 100)
    private String name;

    private String code; // Short game code for joining

    private int maxPlayers = 20;
    private int currentPlayers = 0;

    private LocalDateTime createdAt;
    private LocalDateTime lastUpdated;

    private GameState state = GameState.WAITING;
    private GamePhase currentPhase = GamePhase.SETUP;
    private int dayNumber = 0;

    private String storytellerId; // Firebase UID of storyteller
    private User storyteller; // Transient field for API responses

    private List<Player> players = new ArrayList<>();
    private List<Vote> votes = new ArrayList<>();

    private boolean isActive = true;
    private String storytellerNotes;

    // Constructors
    public Game() {
        this.id = UUID.randomUUID().toString();
        this.code = generateGameCode();
        this.createdAt = LocalDateTime.now();
        this.lastUpdated = LocalDateTime.now();
    }

    public Game(String name, int maxPlayers, User storyteller) {
        this();
        this.name = name;
        this.maxPlayers = maxPlayers;
        this.storyteller = storyteller;
        this.storytellerId = storyteller.getId();
    }

    // Business methods
    public void addPlayer(Player player) {
        if (players.size() >= maxPlayers) {
            throw new IllegalStateException("Game is full");
        }
        if (state != GameState.WAITING) {
            throw new IllegalStateException("Cannot add players to a game that has started");
        }
        
        players.add(player);
        currentPlayers = players.size();
        lastUpdated = LocalDateTime.now();
    }

    public void removePlayer(Player player) {
        players.remove(player);
        currentPlayers = players.size();
        lastUpdated = LocalDateTime.now();
    }

    public void startGame() {
        if (players.size() < 5) {
            throw new IllegalStateException("Need at least 5 players to start");
        }
        this.state = GameState.IN_PROGRESS;
        this.currentPhase = GamePhase.FIRST_NIGHT;
        this.lastUpdated = LocalDateTime.now();
    }

    public void endGame() {
        this.state = GameState.FINISHED;
        this.lastUpdated = LocalDateTime.now();
    }

    public void nextPhase() {
        switch (currentPhase) {
            case SETUP -> currentPhase = GamePhase.FIRST_NIGHT;
            case FIRST_NIGHT -> {
                currentPhase = GamePhase.DAY;
                dayNumber = 1;
            }
            case DAY -> currentPhase = GamePhase.NIGHT;
            case NIGHT -> {
                currentPhase = GamePhase.DAY;
                dayNumber++;
            }
            case VOTING -> currentPhase = GamePhase.DAY;
            case FINISHED -> { /* No transition */ }
        }
        this.lastUpdated = LocalDateTime.now();
    }

    public boolean isStoryteller(String userId) {
        return storytellerId != null && storytellerId.equals(userId);
    }

    public Player getPlayerById(String playerId) {
        return players.stream()
                .filter(p -> p.getId().equals(playerId))
                .findFirst()
                .orElse(null);
    }

    public List<Player> getAlivePlayers() {
        return players.stream()
                .filter(Player::isAlive)
                .toList();
    }

    private String generateGameCode() {
        // Generate a 6-character alphanumeric code
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        StringBuilder code = new StringBuilder();
        for (int i = 0; i < 6; i++) {
            code.append(chars.charAt((int) (Math.random() * chars.length())));
        }
        return code.toString();
    }

    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
        this.lastUpdated = LocalDateTime.now();
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public int getMaxPlayers() {
        return maxPlayers;
    }

    public void setMaxPlayers(int maxPlayers) {
        this.maxPlayers = maxPlayers;
        this.lastUpdated = LocalDateTime.now();
    }

    public int getCurrentPlayers() {
        return currentPlayers;
    }

    public void setCurrentPlayers(int currentPlayers) {
        this.currentPlayers = currentPlayers;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getLastUpdated() {
        return lastUpdated;
    }

    public void setLastUpdated(LocalDateTime lastUpdated) {
        this.lastUpdated = lastUpdated;
    }

    public GameState getState() {
        return state;
    }

    public void setState(GameState state) {
        this.state = state;
        this.lastUpdated = LocalDateTime.now();
    }

    public GamePhase getCurrentPhase() {
        return currentPhase;
    }

    public void setCurrentPhase(GamePhase currentPhase) {
        this.currentPhase = currentPhase;
        this.lastUpdated = LocalDateTime.now();
    }

    public int getDayNumber() {
        return dayNumber;
    }

    public void setDayNumber(int dayNumber) {
        this.dayNumber = dayNumber;
    }

    public String getStorytellerId() {
        return storytellerId;
    }

    public void setStorytellerId(String storytellerId) {
        this.storytellerId = storytellerId;
    }

    public User getStoryteller() {
        return storyteller;
    }

    public void setStoryteller(User storyteller) {
        this.storyteller = storyteller;
        if (storyteller != null) {
            this.storytellerId = storyteller.getId();
        }
    }

    public List<Player> getPlayers() {
        return players;
    }

    public void setPlayers(List<Player> players) {
        this.players = players != null ? players : new ArrayList<>();
        this.currentPlayers = this.players.size();
    }

    public List<Vote> getVotes() {
        return votes;
    }

    public void setVotes(List<Vote> votes) {
        this.votes = votes != null ? votes : new ArrayList<>();
    }

    public boolean isActive() {
        return isActive;
    }

    public void setActive(boolean active) {
        isActive = active;
        this.lastUpdated = LocalDateTime.now();
    }

    public String getStorytellerNotes() {
        return storytellerNotes;
    }

    public void setStorytellerNotes(String storytellerNotes) {
        this.storytellerNotes = storytellerNotes;
        this.lastUpdated = LocalDateTime.now();
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        Game game = (Game) obj;
        return Objects.equals(id, game.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public String toString() {
        return String.format("Game{id='%s', name='%s', code='%s', state=%s, players=%d/%d}",
                id, name, code, state, currentPlayers, maxPlayers);
    }

    /**
     * Game states.
     */
    public enum GameState {
        WAITING,     // Waiting for players to join
        IN_PROGRESS, // Game is active
        PAUSED,      // Game is temporarily paused
        FINISHED     // Game has ended
    }

    /**
     * Game phases.
     */
    public enum GamePhase {
        SETUP,       // Initial setup phase
        FIRST_NIGHT, // First night phase
        DAY,         // Day phase (discussion and voting)
        NIGHT,       // Night phase (role actions)
        VOTING,      // Active voting session
        FINISHED     // Game completed
    }
}
