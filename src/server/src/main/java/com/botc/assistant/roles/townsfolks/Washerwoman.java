package com.botc.assistant.roles.townsfolks;

/**
 * Washerwoman - Townsfolk role
 * 
 * You start knowing that 1 of 2 players is a particular Townsfolk.
 * 
 * The Washerwoman learns information about Townsfolk on the first night.
 * They learn that one of two players is a specific Townsfolk role.
 */
public class Washerwoman extends TownsFolk {
    
    public static final String ROLE_ID = "washerwoman";
    public static final String ROLE_NAME = "Washerwoman";
    public static final String ROLE_DESCRIPTION = "You start knowing that 1 of 2 players is a particular Townsfolk.";
    
    private Washerwoman(Builder builder) {
        super(builder);
    }
    
    public static Builder builder() {
        return new Builder();
    }
    
    public static Washerwoman create() {
        return builder().build();
    }
    
    /**
     * Builder for <PERSON><PERSON>woman role
     */
    public static class Builder extends TownsFolk.Builder<Builder> {
        
        public Builder() {
            id(ROLE_ID);
            name(ROLE_NAME);
            description(ROLE_DESCRIPTION);
            hasNightAbility(true);
            nightPriority(2); // First night information
            setupInfo("Learns that 1 of 2 players is a particular Townsfolk");
        }
        
        @Override
        protected Builder self() {
            return this;
        }
        
        @Override
        public Washerwoman build() {
            return new Washerwoman(this);
        }
    }
}
