package com.botc.assistant.model;

import com.botc.assistant.game.ClientHandler;
import com.botc.assistant.roles.Role;
import java.net.Socket;
import java.util.List;

public class StoryTeller extends ClientHandler {

    public StoryTeller(Socket client, String clientName, boolean isModerator) {
        super(client, clientName, isModerator);
    }

    public void assignRoles(List<Role> roles, List<Player> players) {
        /* TODO */
    }

    public void randomAssignRoles(List<Role> roles, List<Player> players) {
        /* TODO */
    }

    public void createGame(int maxPlayers) {
        /* TODO */
    }

    public void runNightPhase() {
        /* TODO */
    }

    public void runDayPhase() {
        /* TODO */
    }

    public void startVote(int playerPos) {
        /* TODO */
    }

}
