package com.botc.assistant.model;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

/**
 * Represents a voting session in a Blood on the Clock Tower game.
 * Contains nomination information and individual player votes.
 */
public class Vote {

    private String id;
    private String gameId;

    private String nominatorId; // Player who made the nomination
    private String nomineeId;   // Player who was nominated

    private Player nominator; // Transient field for API responses
    private Player nominee;   // Transient field for API responses

    private Map<String, VoteChoice> votes = new HashMap<>(); // playerId -> vote choice
    private VoteResult result = VoteResult.PENDING;

    private boolean isActive = true;
    private LocalDateTime createdAt;
    private LocalDateTime completedAt;

    private int votesFor = 0;
    private int votesAgainst = 0;
    private int abstentions = 0;

    // Constructors
    public Vote() {
        this.id = UUID.randomUUID().toString();
        this.createdAt = LocalDateTime.now();
    }

    public Vote(Player nominator, Player nominee, Game game) {
        this();
        this.nominator = nominator;
        this.nominee = nominee;
        this.nominatorId = nominator.getId();
        this.nomineeId = nominee.getId();
        this.gameId = game.getId();
    }

    // Business methods
    public void castVote(String playerId, VoteChoice choice) {
        if (!isActive) {
            throw new IllegalStateException("Vote is no longer active");
        }

        VoteChoice previousChoice = votes.get(playerId);
        votes.put(playerId, choice);

        // Update vote counts
        if (previousChoice != null) {
            decrementCount(previousChoice);
        }
        incrementCount(choice);
    }

    public void removeVote(String playerId) {
        VoteChoice previousChoice = votes.remove(playerId);
        if (previousChoice != null) {
            decrementCount(previousChoice);
        }
    }

    public void completeVote() {
        this.isActive = false;
        this.completedAt = LocalDateTime.now();
        
        // Determine result
        if (votesFor > votesAgainst) {
            this.result = VoteResult.PASSED;
        } else if (votesAgainst > votesFor) {
            this.result = VoteResult.FAILED;
        } else {
            this.result = VoteResult.TIED;
        }
    }

    public boolean hasPlayerVoted(String playerId) {
        return votes.containsKey(playerId);
    }

    public VoteChoice getPlayerVote(String playerId) {
        return votes.get(playerId);
    }

    public int getTotalVotes() {
        return votes.size();
    }

    public boolean isExecutionVote() {
        return result == VoteResult.PASSED;
    }

    private void incrementCount(VoteChoice choice) {
        switch (choice) {
            case FOR -> votesFor++;
            case AGAINST -> votesAgainst++;
            case ABSTAIN -> abstentions++;
        }
    }

    private void decrementCount(VoteChoice choice) {
        switch (choice) {
            case FOR -> votesFor = Math.max(0, votesFor - 1);
            case AGAINST -> votesAgainst = Math.max(0, votesAgainst - 1);
            case ABSTAIN -> abstentions = Math.max(0, abstentions - 1);
        }
    }

    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getGameId() {
        return gameId;
    }

    public void setGameId(String gameId) {
        this.gameId = gameId;
    }

    public String getNominatorId() {
        return nominatorId;
    }

    public void setNominatorId(String nominatorId) {
        this.nominatorId = nominatorId;
    }

    public String getNomineeId() {
        return nomineeId;
    }

    public void setNomineeId(String nomineeId) {
        this.nomineeId = nomineeId;
    }

    public Player getNominator() {
        return nominator;
    }

    public void setNominator(Player nominator) {
        this.nominator = nominator;
        if (nominator != null) {
            this.nominatorId = nominator.getId();
        }
    }

    public Player getNominee() {
        return nominee;
    }

    public void setNominee(Player nominee) {
        this.nominee = nominee;
        if (nominee != null) {
            this.nomineeId = nominee.getId();
        }
    }

    public Map<String, VoteChoice> getVotes() {
        return votes;
    }

    public void setVotes(Map<String, VoteChoice> votes) {
        this.votes = votes != null ? votes : new HashMap<>();
        recalculateCounts();
    }

    public VoteResult getResult() {
        return result;
    }

    public void setResult(VoteResult result) {
        this.result = result;
    }

    public boolean isActive() {
        return isActive;
    }

    public void setActive(boolean active) {
        isActive = active;
        if (!active && completedAt == null) {
            completedAt = LocalDateTime.now();
        }
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getCompletedAt() {
        return completedAt;
    }

    public void setCompletedAt(LocalDateTime completedAt) {
        this.completedAt = completedAt;
    }

    public int getVotesFor() {
        return votesFor;
    }

    public void setVotesFor(int votesFor) {
        this.votesFor = votesFor;
    }

    public int getVotesAgainst() {
        return votesAgainst;
    }

    public void setVotesAgainst(int votesAgainst) {
        this.votesAgainst = votesAgainst;
    }

    public int getAbstentions() {
        return abstentions;
    }

    public void setAbstentions(int abstentions) {
        this.abstentions = abstentions;
    }

    private void recalculateCounts() {
        votesFor = 0;
        votesAgainst = 0;
        abstentions = 0;
        
        for (VoteChoice choice : votes.values()) {
            incrementCount(choice);
        }
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        Vote vote = (Vote) obj;
        return Objects.equals(id, vote.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public String toString() {
        return String.format("Vote{id='%s', nominator='%s', nominee='%s', result=%s, for=%d, against=%d}",
                id, nominatorId, nomineeId, result, votesFor, votesAgainst);
    }

    /**
     * Individual vote choices.
     */
    public enum VoteChoice {
        FOR,     // Vote to execute
        AGAINST, // Vote against execution
        ABSTAIN  // Abstain from voting
    }

    /**
     * Vote results.
     */
    public enum VoteResult {
        PENDING, // Vote is still in progress
        PASSED,  // Vote passed (execution)
        FAILED,  // Vote failed (no execution)
        TIED     // Vote was tied
    }
}
