package com.botc.assistant.roles.outsiders;

/**
 * Saint - Outsider role
 * 
 * If you die by execution, your team loses.
 * 
 * The Saint has a dangerous drawback if executed.
 */
public class Saint extends Outsider {
    
    public static final String ROLE_ID = "saint";
    public static final String ROLE_NAME = "Saint";
    public static final String ROLE_DESCRIPTION = "If you die by execution, your team loses.";
    
    private Saint(Builder builder) {
        super(builder);
    }
    
    public static Builder builder() {
        return new Builder();
    }
    
    public static Saint create() {
        return builder().build();
    }
    
    /**
     * Builder for Saint role
     */
    public static class Builder extends Outsider.Builder<Builder> {
        
        public Builder() {
            id(ROLE_ID);
            name(ROLE_NAME);
            description(ROLE_DESCRIPTION);
            setupInfo("Good team loses if the Saint is executed");
        }
        
        @Override
        protected Builder self() {
            return this;
        }
        
        @Override
        public Saint build() {
            return new Saint(this);
        }
    }
}
