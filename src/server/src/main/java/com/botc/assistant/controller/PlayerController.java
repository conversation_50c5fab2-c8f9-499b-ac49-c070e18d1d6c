package com.botc.assistant.controller;

import com.botc.assistant.model.Player;
import com.botc.assistant.repository.PlayerRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * REST controller for player management operations.
 */
@RestController
@RequestMapping("/api/games/{gameId}/players")
@CrossOrigin(origins = { "http://localhost:5173", "http://localhost:5174", "http://localhost:3000" })
public class PlayerController {

    @Autowired
    private PlayerRepository playerRepository;

    /**
     * Get all players in a game.
     */
    @GetMapping
    public ResponseEntity<List<Player>> getGamePlayers(@PathVariable String gameId) {
        try {
            List<Player> players = playerRepository.findByGameId(gameId);
            return ResponseEntity.ok(players);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Get a specific player.
     */
    @GetMapping("/{playerId}")
    public ResponseEntity<Player> getPlayer(@PathVariable String gameId, @PathVariable String playerId) {
        try {
            return playerRepository.findById(playerId)
                    .map(ResponseEntity::ok)
                    .orElse(ResponseEntity.notFound().build());
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Update player status.
     */
    @PutMapping("/{playerId}")
    public ResponseEntity<Void> updatePlayer(@PathVariable String gameId, 
                                            @PathVariable String playerId,
                                            @RequestBody UpdatePlayerRequest request) {
        try {
            var updates = new java.util.HashMap<String, Object>();
            
            if (request.getAlive() != null) {
                updates.put("alive", request.getAlive());
                updates.put("canVote", request.getAlive()); // Dead players can't vote
            }
            
            if (request.getCanVote() != null) {
                updates.put("canVote", request.getCanVote());
            }
            
            if (request.getNominated() != null) {
                updates.put("nominated", request.getNominated());
            }
            
            if (request.getHasVoted() != null) {
                updates.put("hasVoted", request.getHasVoted());
            }
            
            if (request.getNotes() != null) {
                updates.put("notes", request.getNotes());
            }
            
            playerRepository.updateFields(playerId, updates);
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Get alive players in a game.
     */
    @GetMapping("/alive")
    public ResponseEntity<List<Player>> getAlivePlayers(@PathVariable String gameId) {
        try {
            List<Player> players = playerRepository.findAliveByGameId(gameId);
            return ResponseEntity.ok(players);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    // Request DTOs
    public static class UpdatePlayerRequest {
        private Boolean alive;
        private Boolean canVote;
        private Boolean nominated;
        private Boolean hasVoted;
        private String notes;

        // Getters and setters
        public Boolean getAlive() { return alive; }
        public void setAlive(Boolean alive) { this.alive = alive; }
        public Boolean getCanVote() { return canVote; }
        public void setCanVote(Boolean canVote) { this.canVote = canVote; }
        public Boolean getNominated() { return nominated; }
        public void setNominated(Boolean nominated) { this.nominated = nominated; }
        public Boolean getHasVoted() { return hasVoted; }
        public void setHasVoted(Boolean hasVoted) { this.hasVoted = hasVoted; }
        public String getNotes() { return notes; }
        public void setNotes(String notes) { this.notes = notes; }
    }
}
