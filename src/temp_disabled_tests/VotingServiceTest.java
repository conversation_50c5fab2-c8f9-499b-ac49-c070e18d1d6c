package com.botc.assistant.service;

import com.botc.assistant.model.Game;
import com.botc.assistant.model.GamePhase;
import com.botc.assistant.model.Player;
import com.botc.assistant.model.User;
import com.botc.assistant.model.Vote;
import com.botc.assistant.repository.VoteRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for VotingService.
 */
@ExtendWith(MockitoExtension.class)
class VotingServiceTest {

    @Mock
    private VoteRepository voteRepository;

    @Mock
    private NotificationService notificationService;

    @Mock
    private PhaseService phaseService;

    @InjectMocks
    private VotingService votingService;

    private Game testGame;
    private Player nominator;
    private Player nominee;
    private Player voter;
    private Vote testVote;
    private GamePhase dayPhase;

    @BeforeEach
    void setUp() {
        User storyteller = new User("storyteller", "<EMAIL>", "password");
        storyteller.setId("st-id");

        testGame = new Game("Test Game", 10, storyteller);
        testGame.setId("game-id");

        nominator = new Player("Nominator", 0, testGame);
        nominator.setId("nominator-id");
        nominator.setAlive(true);

        nominee = new Player("Nominee", 1, testGame);
        nominee.setId("nominee-id");
        nominee.setAlive(true);

        voter = new Player("Voter", 2, testGame);
        voter.setId("voter-id");
        voter.setAlive(true);
        voter.setCanVote(true);

        testGame.getPlayers().addAll(Arrays.asList(nominator, nominee, voter));

        testVote = new Vote(nominator, nominee, testGame);
        testVote.setId("vote-id");

        dayPhase = new GamePhase(GamePhase.PhaseType.DAY, 1, testGame);
        testGame.setCurrentPhase(dayPhase);
    }

    @Test
    void nominatePlayer_ValidNomination_ReturnsVote() {
        // Given
        when(voteRepository.findActiveVoteInGame(testGame)).thenReturn(Optional.empty());
        when(voteRepository.save(any(Vote.class))).thenReturn(testVote);

        // When
        Vote result = votingService.nominatePlayer(testGame, nominator, nominee);

        // Then
        assertNotNull(result);
        assertEquals(nominator, result.getNominator());
        assertEquals(nominee, result.getNominee());
        assertEquals(testGame, result.getGame());
        assertTrue(nominee.isNominated());
        assertEquals(testVote, testGame.getCurrentVote());

        verify(voteRepository).findActiveVoteInGame(testGame);
        verify(voteRepository).save(any(Vote.class));
        verify(notificationService).notifyNomination(testGame, result);
    }

    @Test
    void nominatePlayer_ActiveVoteExists_ThrowsException() {
        // Given
        when(voteRepository.findActiveVoteInGame(testGame)).thenReturn(Optional.of(testVote));

        // When & Then
        IllegalStateException exception = assertThrows(IllegalStateException.class,
                () -> votingService.nominatePlayer(testGame, nominator, nominee));

        assertEquals("Another vote is already in progress", exception.getMessage());
        verify(voteRepository).findActiveVoteInGame(testGame);
        verify(voteRepository, never()).save(any(Vote.class));
    }

    @Test
    void nominatePlayer_DeadNominator_ThrowsException() {
        // Given
        nominator.setAlive(false);

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
                () -> votingService.nominatePlayer(testGame, nominator, nominee));

        assertEquals("Dead players cannot nominate", exception.getMessage());
        verify(voteRepository, never()).save(any(Vote.class));
    }

    @Test
    void nominatePlayer_DeadNominee_ThrowsException() {
        // Given
        nominee.setAlive(false);

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
                () -> votingService.nominatePlayer(testGame, nominator, nominee));

        assertEquals("Cannot nominate dead players", exception.getMessage());
        verify(voteRepository, never()).save(any(Vote.class));
    }

    @Test
    void nominatePlayer_AlreadyNominated_ThrowsException() {
        // Given
        nominee.setNominated(true);

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
                () -> votingService.nominatePlayer(testGame, nominator, nominee));

        assertEquals("Player is already nominated", exception.getMessage());
        verify(voteRepository, never()).save(any(Vote.class));
    }

    @Test
    void nominatePlayer_SelfNomination_ThrowsException() {
        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
                () -> votingService.nominatePlayer(testGame, nominator, nominator));

        assertEquals("Players cannot nominate themselves", exception.getMessage());
        verify(voteRepository, never()).save(any(Vote.class));
    }

    @Test
    void startVoting_ValidVote_StartsVoting() {
        // Given
        testGame.setCurrentVote(testVote);
        testVote.setStatus(Vote.VoteStatus.NOMINATED);

        // When
        votingService.startVoting(testGame);

        // Then
        verify(voteRepository).save(testVote);
        verify(notificationService).notifyVotingStarted(testGame, testVote);
        assertEquals(Vote.VoteStatus.VOTING, testVote.getStatus());
    }

    @Test
    void startVoting_NoCurrentVote_ThrowsException() {
        // Given
        testGame.setCurrentVote(null);

        // When & Then
        IllegalStateException exception = assertThrows(IllegalStateException.class,
                () -> votingService.startVoting(testGame));

        assertEquals("No nomination to vote on", exception.getMessage());
        verify(voteRepository, never()).save(any(Vote.class));
    }

    @Test
    void startVoting_WrongStatus_ThrowsException() {
        // Given
        testGame.setCurrentVote(testVote);
        testVote.setStatus(Vote.VoteStatus.VOTING);

        // When & Then
        IllegalStateException exception = assertThrows(IllegalStateException.class,
                () -> votingService.startVoting(testGame));

        assertEquals("Cannot start voting - nomination not in correct state", exception.getMessage());
        verify(voteRepository, never()).save(any(Vote.class));
    }

    @Test
    void castVote_ValidVote_ReturnsTrue() {
        // Given
        testGame.setCurrentVote(testVote);
        testVote.setStatus(Vote.VoteStatus.VOTING);
        testVote.startVoting();

        // When
        boolean result = votingService.castVote(testGame, voter, Vote.VoteChoice.YES);

        // Then
        assertTrue(result);
        assertTrue(voter.hasVoted());
        verify(voteRepository).save(testVote);
        verify(notificationService).notifyVoteCast(testGame, voter, Vote.VoteChoice.YES);
    }

    @Test
    void castVote_NoActiveVote_ThrowsException() {
        // Given
        testGame.setCurrentVote(null);

        // When & Then
        IllegalStateException exception = assertThrows(IllegalStateException.class,
                () -> votingService.castVote(testGame, voter, Vote.VoteChoice.YES));

        assertEquals("No active voting session", exception.getMessage());
        verify(voteRepository, never()).save(any(Vote.class));
    }

    @Test
    void castVote_DeadVoter_ThrowsException() {
        // Given
        testGame.setCurrentVote(testVote);
        testVote.setStatus(Vote.VoteStatus.VOTING);
        voter.setAlive(false);

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
                () -> votingService.castVote(testGame, voter, Vote.VoteChoice.YES));

        assertEquals("Player cannot vote", exception.getMessage());
        verify(voteRepository, never()).save(any(Vote.class));
    }

    @Test
    void castVote_CannotVote_ThrowsException() {
        // Given
        testGame.setCurrentVote(testVote);
        testVote.setStatus(Vote.VoteStatus.VOTING);
        voter.setCanVote(false);

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
                () -> votingService.castVote(testGame, voter, Vote.VoteChoice.YES));

        assertEquals("Player cannot vote", exception.getMessage());
        verify(voteRepository, never()).save(any(Vote.class));
    }

    @Test
    void endVoting_ValidVote_EndsVoting() {
        // Given
        testGame.setCurrentVote(testVote);
        testVote.setStatus(Vote.VoteStatus.VOTING);
        testVote.startVoting();
        testVote.castVote(voter, Vote.VoteChoice.YES);
        // Don't call endVoting here - let the service method do it

        // When
        Vote.VoteResult result = votingService.endVoting(testGame);

        // Then
        assertNotNull(result);
        assertFalse(nominee.isNominated());
        assertNull(testGame.getCurrentVote());
        verify(voteRepository).save(testVote);
        verify(notificationService).notifyVotingEnded(testGame, testVote, result);
    }

    @Test
    void endVoting_NoActiveVote_ThrowsException() {
        // Given
        testGame.setCurrentVote(null);

        // When & Then
        IllegalStateException exception = assertThrows(IllegalStateException.class,
                () -> votingService.endVoting(testGame));

        assertEquals("No active voting session to end", exception.getMessage());
        verify(voteRepository, never()).save(any(Vote.class));
    }

    @Test
    void cancelVote_ValidVote_CancelsVote() {
        // Given
        testGame.setCurrentVote(testVote);
        nominee.setNominated(true);

        // When
        votingService.cancelVote(testGame);

        // Then
        assertFalse(nominee.isNominated());
        assertNull(testGame.getCurrentVote());
        verify(voteRepository).save(testVote);
        verify(notificationService).notifyVoteCancelled(testGame, testVote);
    }

    @Test
    void cancelVote_NoCurrentVote_ThrowsException() {
        // Given
        testGame.setCurrentVote(null);

        // When & Then
        IllegalStateException exception = assertThrows(IllegalStateException.class,
                () -> votingService.cancelVote(testGame));

        assertEquals("No vote to cancel", exception.getMessage());
        verify(voteRepository, never()).save(any(Vote.class));
    }

    @Test
    void getVotingHistory_ReturnsHistory() {
        // Given
        List<Vote> history = Arrays.asList(testVote);
        when(voteRepository.findByGameOrderByCreatedAtDesc(testGame)).thenReturn(history);

        // When
        List<Vote> result = votingService.getVotingHistory(testGame);

        // Then
        assertEquals(1, result.size());
        assertEquals(testVote, result.get(0));
        verify(voteRepository).findByGameOrderByCreatedAtDesc(testGame);
    }

    @Test
    void canNominate_ValidPlayer_ReturnsTrue() {
        // Given
        when(voteRepository.findActiveVoteInGame(testGame)).thenReturn(Optional.empty());

        // When
        boolean result = votingService.canNominate(testGame, nominator);

        // Then
        assertTrue(result);
        verify(voteRepository).findActiveVoteInGame(testGame);
    }

    @Test
    void canNominate_DeadPlayer_ReturnsFalse() {
        // Given
        nominator.setAlive(false);

        // When
        boolean result = votingService.canNominate(testGame, nominator);

        // Then
        assertFalse(result);
        verify(voteRepository, never()).findActiveVoteInGame(any(Game.class));
    }

    @Test
    void canNominate_ActiveVoteExists_ReturnsFalse() {
        // Given
        when(voteRepository.findActiveVoteInGame(testGame)).thenReturn(Optional.of(testVote));

        // When
        boolean result = votingService.canNominate(testGame, nominator);

        // Then
        assertFalse(result);
        verify(voteRepository).findActiveVoteInGame(testGame);
    }

    @Test
    void canBeNominated_ValidPlayer_ReturnsTrue() {
        // When
        boolean result = votingService.canBeNominated(nominee);

        // Then
        assertTrue(result);
    }

    @Test
    void canBeNominated_DeadPlayer_ReturnsFalse() {
        // Given
        nominee.setAlive(false);

        // When
        boolean result = votingService.canBeNominated(nominee);

        // Then
        assertFalse(result);
    }

    @Test
    void canBeNominated_AlreadyNominated_ReturnsFalse() {
        // Given
        nominee.setNominated(true);

        // When
        boolean result = votingService.canBeNominated(nominee);

        // Then
        assertFalse(result);
    }

    @Test
    void getCurrentVoteStatus_NoCurrentVote_ReturnsInactiveStatus() {
        // Given
        testGame.setCurrentVote(null);

        // When
        VotingService.VoteStatus result = votingService.getCurrentVoteStatus(testGame);

        // Then
        assertFalse(result.isVotingInProgress());
        assertNull(result.getNominatorName());
        assertNull(result.getNomineeName());
        assertEquals(0, result.getVotesFor());
        assertEquals(0, result.getVotesAgainst());
        assertEquals(0, result.getAbstentions());
    }

    @Test
    void getCurrentVoteStatus_ActiveVote_ReturnsActiveStatus() {
        // Given
        testGame.setCurrentVote(testVote);
        testVote.setStatus(Vote.VoteStatus.VOTING);
        testVote.startVoting();

        // When
        VotingService.VoteStatus result = votingService.getCurrentVoteStatus(testGame);

        // Then
        assertTrue(result.isVotingInProgress());
        assertEquals("Nominator", result.getNominatorName());
        assertEquals("Nominee", result.getNomineeName());
    }
}
