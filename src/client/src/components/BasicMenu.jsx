import * as React from 'react';
import { styled, alpha } from '@mui/material/styles';
import Button from '@mui/material/Button';
import Menu from '@mui/material/Menu';
import MenuItem from '@mui/material/MenuItem';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';

const StyledMenu = styled((props) => (
  <Menu
    elevation={0}
    anchorOrigin={{
      vertical: 'bottom',
      horizontal: 'right',
    }}
    transformOrigin={{
      vertical: 'top',
      horizontal: 'right',
    }}
    {...props}
  />
))(({ theme }) => ({
  '& .MuiPaper-root': {
    borderRadius: 6,
    marginTop: theme.spacing(1),
    minWidth: 180,
    color: 'rgb(46, 15, 56)',
    backgroundColor: 'rgb(255, 251, 157)',
    boxShadow:
      'rgb(255, 255, 255) 0px 0px 0px 0px, rgba(0, 0, 0, 0.05) 0px 0px 0px 1px, rgba(0, 0, 0, 0.1) 0px 10px 15px -3px, rgba(0, 0, 0, 0.05) 0px 4px 6px -2px',
    '& .MuiMenu-list': {
      padding: '4px 0',
    },
    '& .MuiMenuItem-root': {
      color: 'rgb(46, 15, 56)', 
      backgroundColor: alpha(
          'rgb(255, 251, 157)',
          theme.palette.action.selectedOpacity,
        ),
      fontWeight: 500,
      '&:hover': {
        backgroundColor: 'rgba(255, 237, 157, 1)', 
        color: 'rgb(46, 15, 56)', 
      },
      '&:active': {
        backgroundColor: alpha(
          'rgb(255, 251, 157)',
          theme.palette.action.selectedOpacity,
        ),
      },
      '& a': {
        color: 'rgb(46, 15, 56)',
        textDecoration: 'none',
        display: 'block',
        width: '100%',
      },
    },
  },
}));

export default function CustomizedMenus() {
  const [anchorEl, setAnchorEl] = React.useState(null);
  const open = Boolean(anchorEl);
  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };

  return (
    <div>
      <Button
        id="demo-customized-button"
        aria-controls={open ? 'demo-customized-menu' : undefined}
        aria-haspopup="true"
        aria-expanded={open ? 'true' : undefined}
        variant="contained"
        disableElevation
        onClick={handleClick}
        endIcon={<KeyboardArrowDownIcon />}
        sx={{
            backgroundColor: 'rgb(255, 251, 157)', 
            color: 'rgb(46, 15, 56)', 
            '&:hover': {
            backgroundColor: 'rgb(240, 240, 120)', 
            },
        }}
      >
        Jouer
      </Button>
      <StyledMenu
        id="demo-customized-menu"
        slotProps={{
          list: {
            'aria-labelledby': 'demo-customized-button',
          },
        }}
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
      >
        <MenuItem onClick={handleClose} disableRipple>
          <a href="/join">Rejoindre une partie</a>
        </MenuItem>
        <MenuItem onClick={handleClose} disableRipple>
          <a href="/create">Lancer une partie</a>
        </MenuItem>
      </StyledMenu>
    </div>
  );
}
