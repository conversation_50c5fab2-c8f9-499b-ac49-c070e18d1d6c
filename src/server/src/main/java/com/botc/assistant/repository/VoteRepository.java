package com.botc.assistant.repository;

import com.botc.assistant.model.Vote;
import com.google.cloud.firestore.DocumentSnapshot;
import com.google.cloud.firestore.Firestore;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ExecutionException;

/**
 * Repository for managing Vote entities in Firestore.
 */
@Repository
public class VoteRepository {

    private final Firestore firestore;
    private static final String COLLECTION_NAME = "votes";

    @Autowired
    public VoteRepository(@Autowired(required = false) Firestore firestore) {
        this.firestore = firestore;
    }

    /**
     * Save a vote to Firestore.
     */
    public Vote save(Vote vote) {
        if (firestore == null) {
            throw new RuntimeException("Firestore is not configured");
        }

        try {
            Map<String, Object> voteData = voteToMap(vote);
            firestore.collection(COLLECTION_NAME).document(vote.getId()).set(voteData);
            return vote;
        } catch (Exception e) {
            throw new RuntimeException("Failed to save vote: " + e.getMessage(), e);
        }
    }

    /**
     * Find a vote by ID.
     */
    public Optional<Vote> findById(String id) {
        if (firestore == null) {
            return Optional.empty();
        }

        try {
            var document = firestore.collection(COLLECTION_NAME).document(id).get().get();
            if (document.exists()) {
                return Optional.of(documentToVote(document));
            }
            return Optional.empty();
        } catch (InterruptedException | ExecutionException e) {
            System.err.println("Error finding vote by ID: " + e.getMessage());
            return Optional.empty();
        }
    }

    /**
     * Find all votes for a game.
     */
    public List<Vote> findByGameId(String gameId) {
        if (firestore == null) {
            return new ArrayList<>();
        }

        try {
            List<Vote> votes = new ArrayList<>();
            var documents = firestore.collection(COLLECTION_NAME)
                    .whereEqualTo("gameId", gameId)
                    .orderBy("createdAt")
                    .get().get();
            
            for (var document : documents.getDocuments()) {
                votes.add(documentToVote(document));
            }
            
            return votes;
        } catch (InterruptedException | ExecutionException e) {
            System.err.println("Error retrieving votes by game ID: " + e.getMessage());
            return new ArrayList<>();
        }
    }

    /**
     * Find active votes for a game.
     */
    public List<Vote> findActiveByGameId(String gameId) {
        if (firestore == null) {
            return new ArrayList<>();
        }

        try {
            List<Vote> votes = new ArrayList<>();
            var documents = firestore.collection(COLLECTION_NAME)
                    .whereEqualTo("gameId", gameId)
                    .whereEqualTo("isActive", true)
                    .orderBy("createdAt")
                    .get().get();
            
            for (var document : documents.getDocuments()) {
                votes.add(documentToVote(document));
            }
            
            return votes;
        } catch (InterruptedException | ExecutionException e) {
            System.err.println("Error retrieving active votes: " + e.getMessage());
            return new ArrayList<>();
        }
    }

    /**
     * Find the current active vote for a game.
     */
    public Optional<Vote> findCurrentActiveVote(String gameId) {
        List<Vote> activeVotes = findActiveByGameId(gameId);
        return activeVotes.isEmpty() ? Optional.empty() : Optional.of(activeVotes.get(activeVotes.size() - 1));
    }

    /**
     * Delete a vote by ID.
     */
    public void deleteById(String id) {
        if (firestore == null) {
            return;
        }

        try {
            firestore.collection(COLLECTION_NAME).document(id).delete();
        } catch (Exception e) {
            System.err.println("Error deleting vote: " + e.getMessage());
        }
    }

    /**
     * Delete all votes for a game.
     */
    public void deleteByGameId(String gameId) {
        if (firestore == null) {
            return;
        }

        try {
            var documents = firestore.collection(COLLECTION_NAME)
                    .whereEqualTo("gameId", gameId)
                    .get().get();
            
            for (var document : documents.getDocuments()) {
                document.getReference().delete();
            }
        } catch (InterruptedException | ExecutionException e) {
            System.err.println("Error deleting votes by game ID: " + e.getMessage());
        }
    }

    /**
     * Update specific fields of a vote.
     */
    public void updateFields(String id, Map<String, Object> updates) {
        if (firestore == null) {
            return;
        }

        try {
            firestore.collection(COLLECTION_NAME).document(id).update(updates);
        } catch (Exception e) {
            System.err.println("Error updating vote fields: " + e.getMessage());
        }
    }

    /**
     * Convert Vote object to Firestore document map.
     */
    private Map<String, Object> voteToMap(Vote vote) {
        Map<String, Object> data = new HashMap<>();
        data.put("id", vote.getId());
        data.put("gameId", vote.getGameId());
        data.put("nominatorId", vote.getNominatorId());
        data.put("nomineeId", vote.getNomineeId());
        data.put("result", vote.getResult().name());
        data.put("isActive", vote.isActive());
        data.put("createdAt", vote.getCreatedAt().toString());
        
        if (vote.getCompletedAt() != null) {
            data.put("completedAt", vote.getCompletedAt().toString());
        }
        
        data.put("votesFor", vote.getVotesFor());
        data.put("votesAgainst", vote.getVotesAgainst());
        data.put("abstentions", vote.getAbstentions());
        
        // Convert vote choices map to string keys for Firestore
        Map<String, String> voteChoices = new HashMap<>();
        for (Map.Entry<String, Vote.VoteChoice> entry : vote.getVotes().entrySet()) {
            voteChoices.put(entry.getKey(), entry.getValue().name());
        }
        data.put("votes", voteChoices);
        
        return data;
    }

    /**
     * Convert Firestore document to Vote object.
     */
    @SuppressWarnings("unchecked")
    private Vote documentToVote(DocumentSnapshot document) {
        Vote vote = new Vote();
        
        vote.setId(document.getString("id"));
        vote.setGameId(document.getString("gameId"));
        vote.setNominatorId(document.getString("nominatorId"));
        vote.setNomineeId(document.getString("nomineeId"));
        
        String resultStr = document.getString("result");
        if (resultStr != null) {
            vote.setResult(Vote.VoteResult.valueOf(resultStr));
        }
        
        Boolean isActive = document.getBoolean("isActive");
        if (isActive != null) {
            vote.setActive(isActive);
        }
        
        String createdAtStr = document.getString("createdAt");
        if (createdAtStr != null) {
            vote.setCreatedAt(LocalDateTime.parse(createdAtStr));
        }
        
        String completedAtStr = document.getString("completedAt");
        if (completedAtStr != null) {
            vote.setCompletedAt(LocalDateTime.parse(completedAtStr));
        }
        
        Long votesFor = document.getLong("votesFor");
        if (votesFor != null) {
            vote.setVotesFor(votesFor.intValue());
        }
        
        Long votesAgainst = document.getLong("votesAgainst");
        if (votesAgainst != null) {
            vote.setVotesAgainst(votesAgainst.intValue());
        }
        
        Long abstentions = document.getLong("abstentions");
        if (abstentions != null) {
            vote.setAbstentions(abstentions.intValue());
        }
        
        // Convert string vote choices back to enum map
        Map<String, String> voteChoicesStr = (Map<String, String>) document.get("votes");
        if (voteChoicesStr != null) {
            Map<String, Vote.VoteChoice> voteChoices = new HashMap<>();
            for (Map.Entry<String, String> entry : voteChoicesStr.entrySet()) {
                voteChoices.put(entry.getKey(), Vote.VoteChoice.valueOf(entry.getValue()));
            }
            vote.setVotes(voteChoices);
        }
        
        return vote;
    }
}
