package com.botc.assistant.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * Service for sending real-time notifications via WebSocket.
 * Handles game events, chat messages, and system notifications.
 */
@Service
public class NotificationService {

    private final SimpMessagingTemplate messagingTemplate;

    @Autowired
    public NotificationService(SimpMessagingTemplate messagingTemplate) {
        this.messagingTemplate = messagingTemplate;
    }

    /**
     * Send a game event notification to all players in a game.
     */
    public void sendGameEvent(String gameId, String eventType, Object eventData) {
        Map<String, Object> notification = new HashMap<>();
        notification.put("type", "game.event");
        notification.put("eventType", eventType);
        notification.put("data", eventData);
        notification.put("timestamp", LocalDateTime.now().toString());

        messagingTemplate.convertAndSend("/topic/game/" + gameId, notification);
    }

    /**
     * Send a phase change notification.
     */
    public void sendPhaseChange(String gameId, String newPhase, int dayNumber) {
        Map<String, Object> data = new HashMap<>();
        data.put("phase", newPhase);
        data.put("dayNumber", dayNumber);

        sendGameEvent(gameId, "phase.change", data);
    }

    /**
     * Send a player join notification.
     */
    public void sendPlayerJoined(String gameId, String playerName, int playerCount) {
        Map<String, Object> data = new HashMap<>();
        data.put("playerName", playerName);
        data.put("playerCount", playerCount);

        sendGameEvent(gameId, "player.joined", data);
    }

    /**
     * Send a player leave notification.
     */
    public void sendPlayerLeft(String gameId, String playerName, int playerCount) {
        Map<String, Object> data = new HashMap<>();
        data.put("playerName", playerName);
        data.put("playerCount", playerCount);

        sendGameEvent(gameId, "player.left", data);
    }

    /**
     * Send a nomination notification.
     */
    public void sendNomination(String gameId, String nominatorName, String nomineeName) {
        Map<String, Object> data = new HashMap<>();
        data.put("nominator", nominatorName);
        data.put("nominee", nomineeName);

        sendGameEvent(gameId, "vote.nomination", data);
    }

    /**
     * Send a vote cast notification.
     */
    public void sendVoteCast(String gameId, String voterName, String choice) {
        Map<String, Object> data = new HashMap<>();
        data.put("voter", voterName);
        data.put("choice", choice);

        sendGameEvent(gameId, "vote.cast", data);
    }

    /**
     * Send vote results notification.
     */
    public void sendVoteResults(String gameId, String nomineeName, String result, 
                               int votesFor, int votesAgainst, int abstentions) {
        Map<String, Object> data = new HashMap<>();
        data.put("nominee", nomineeName);
        data.put("result", result);
        data.put("votesFor", votesFor);
        data.put("votesAgainst", votesAgainst);
        data.put("abstentions", abstentions);

        sendGameEvent(gameId, "vote.results", data);
    }

    /**
     * Send a player death notification.
     */
    public void sendPlayerDeath(String gameId, String playerName, String cause) {
        Map<String, Object> data = new HashMap<>();
        data.put("playerName", playerName);
        data.put("cause", cause);

        sendGameEvent(gameId, "player.death", data);
    }

    /**
     * Send a game start notification.
     */
    public void sendGameStart(String gameId, int playerCount) {
        Map<String, Object> data = new HashMap<>();
        data.put("playerCount", playerCount);

        sendGameEvent(gameId, "game.start", data);
    }

    /**
     * Send a game end notification.
     */
    public void sendGameEnd(String gameId, String winner, String reason) {
        Map<String, Object> data = new HashMap<>();
        data.put("winner", winner);
        data.put("reason", reason);

        sendGameEvent(gameId, "game.end", data);
    }

    /**
     * Send a chat message to all players in a game.
     */
    public void sendChatMessage(String gameId, String senderName, String message) {
        Map<String, Object> notification = new HashMap<>();
        notification.put("type", "chat.message");
        notification.put("sender", senderName);
        notification.put("message", message);
        notification.put("timestamp", LocalDateTime.now().toString());

        messagingTemplate.convertAndSend("/topic/game/" + gameId, notification);
    }

    /**
     * Send a private message to a specific player.
     */
    public void sendPrivateMessage(String gameId, String playerId, String message) {
        Map<String, Object> notification = new HashMap<>();
        notification.put("type", "private.message");
        notification.put("message", message);
        notification.put("timestamp", LocalDateTime.now().toString());

        messagingTemplate.convertAndSendToUser(playerId, "/queue/game/" + gameId, notification);
    }

    /**
     * Send a system notification to all players in a game.
     */
    public void sendSystemNotification(String gameId, String message, String level) {
        Map<String, Object> notification = new HashMap<>();
        notification.put("type", "system.notification");
        notification.put("message", message);
        notification.put("level", level); // info, warning, error
        notification.put("timestamp", LocalDateTime.now().toString());

        messagingTemplate.convertAndSend("/topic/game/" + gameId, notification);
    }

    /**
     * Send a custom notification with arbitrary data.
     */
    public void sendCustomMessage(String gameId, String message, String messageType) {
        Map<String, Object> notification = new HashMap<>();
        notification.put("type", messageType);
        notification.put("message", message);
        notification.put("timestamp", LocalDateTime.now().toString());

        messagingTemplate.convertAndSend("/topic/game/" + gameId, notification);
    }

    /**
     * Send role assignment notification to a specific player.
     */
    public void sendRoleAssignment(String gameId, String playerId, String roleName, String roleDescription) {
        Map<String, Object> data = new HashMap<>();
        data.put("roleName", roleName);
        data.put("roleDescription", roleDescription);

        Map<String, Object> notification = new HashMap<>();
        notification.put("type", "role.assignment");
        notification.put("data", data);
        notification.put("timestamp", LocalDateTime.now().toString());

        messagingTemplate.convertAndSendToUser(playerId, "/queue/game/" + gameId, notification);
    }

    /**
     * Send storyteller notification (private to storyteller).
     */
    public void sendStorytellerNotification(String gameId, String storytellerId, String message, Object data) {
        Map<String, Object> notification = new HashMap<>();
        notification.put("type", "storyteller.notification");
        notification.put("message", message);
        notification.put("data", data);
        notification.put("timestamp", LocalDateTime.now().toString());

        messagingTemplate.convertAndSendToUser(storytellerId, "/queue/game/" + gameId, notification);
    }
}
