package com.botc.assistant.roles.minions;

/**
 * <PERSON> Woman - Minion role
 * 
 * If there are 5 or more players alive & the Demon dies, you become the Demon. (Travellers don't count)
 * 
 * The Scarlet Woman can become the Demon if the original Demon dies.
 */
public class <PERSON>_Woman extends Minion {
    
    public static final String ROLE_ID = "scarlet_woman";
    public static final String ROLE_NAME = "Scarlet Woman";
    public static final String ROLE_DESCRIPTION = "If there are 5 or more players alive & the Demon dies, you become the Demon. (Travellers don't count)";
    
    private Scarlet_Woman(Builder builder) {
        super(builder);
    }
    
    public static Builder builder() {
        return new Builder();
    }
    
    public static <PERSON>_Woman create() {
        return builder().build();
    }
    
    /**
     * Builder for Scarlet Woman role
     */
    public static class Builder extends Minion.Builder<Builder> {
        
        public Builder() {
            id(ROLE_ID);
            name(ROLE_NAME);
            description(ROLE_DESCRIPTION);
            setupInfo("Becomes the Demon if original <PERSON> dies with 5+ players alive");
        }
        
        @Override
        protected Builder self() {
            return this;
        }
        
        @Override
        public Scarlet_Woman build() {
            return new <PERSON>_Woman(this);
        }
    }
}
