// Firestore service functions for game data
import { 
  collection, 
  doc, 
  getDoc, 
  getDocs, 
  setDoc, 
  updateDoc, 
  deleteDoc, 
  query, 
  where, 
  orderBy, 
  onSnapshot,
  addDoc
} from 'firebase/firestore';
import { db } from './config.js';

/**
 * Users collection functions
 */
export const getUsers = async () => {
  try {
    const querySnapshot = await getDocs(collection(db, 'users'));
    return querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
  } catch (error) {
    console.error('Error getting users:', error);
    return [];
  }
};

export const getUser = async (userId) => {
  try {
    const docSnap = await getDoc(doc(db, 'users', userId));
    return docSnap.exists() ? { id: docSnap.id, ...docSnap.data() } : null;
  } catch (error) {
    console.error('Error getting user:', error);
    return null;
  }
};

/**
 * Games collection functions
 */
export const createGame = async (gameData) => {
  try {
    const docRef = await addDoc(collection(db, 'games'), {
      ...gameData,
      createdAt: new Date().toISOString(),
      lastUpdated: new Date().toISOString()
    });
    return { id: docRef.id, ...gameData };
  } catch (error) {
    console.error('Error creating game:', error);
    throw error;
  }
};

export const getGame = async (gameId) => {
  try {
    const docSnap = await getDoc(doc(db, 'games', gameId));
    return docSnap.exists() ? { id: docSnap.id, ...docSnap.data() } : null;
  } catch (error) {
    console.error('Error getting game:', error);
    return null;
  }
};

export const getGameByCode = async (code) => {
  try {
    const q = query(
      collection(db, 'games'), 
      where('code', '==', code),
      where('isActive', '==', true)
    );
    const querySnapshot = await getDocs(q);
    
    if (!querySnapshot.empty) {
      const doc = querySnapshot.docs[0];
      return { id: doc.id, ...doc.data() };
    }
    return null;
  } catch (error) {
    console.error('Error getting game by code:', error);
    return null;
  }
};

export const getActiveGames = async () => {
  try {
    const q = query(
      collection(db, 'games'), 
      where('isActive', '==', true),
      orderBy('createdAt', 'desc')
    );
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
  } catch (error) {
    console.error('Error getting active games:', error);
    return [];
  }
};

export const getGamesByStoryteller = async (storytellerId) => {
  try {
    const q = query(
      collection(db, 'games'), 
      where('storytellerId', '==', storytellerId),
      orderBy('createdAt', 'desc')
    );
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
  } catch (error) {
    console.error('Error getting games by storyteller:', error);
    return [];
  }
};

export const updateGame = async (gameId, updates) => {
  try {
    await updateDoc(doc(db, 'games', gameId), {
      ...updates,
      lastUpdated: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error updating game:', error);
    throw error;
  }
};

export const deleteGame = async (gameId) => {
  try {
    await deleteDoc(doc(db, 'games', gameId));
  } catch (error) {
    console.error('Error deleting game:', error);
    throw error;
  }
};

/**
 * Players collection functions
 */
export const createPlayer = async (playerData) => {
  try {
    const docRef = await addDoc(collection(db, 'players'), {
      ...playerData,
      joinedAt: new Date().toISOString(),
      lastActivity: new Date().toISOString()
    });
    return { id: docRef.id, ...playerData };
  } catch (error) {
    console.error('Error creating player:', error);
    throw error;
  }
};

export const getPlayersByGame = async (gameId) => {
  try {
    const q = query(
      collection(db, 'players'), 
      where('gameId', '==', gameId),
      orderBy('position')
    );
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
  } catch (error) {
    console.error('Error getting players by game:', error);
    return [];
  }
};

export const updatePlayer = async (playerId, updates) => {
  try {
    await updateDoc(doc(db, 'players', playerId), {
      ...updates,
      lastActivity: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error updating player:', error);
    throw error;
  }
};

export const deletePlayer = async (playerId) => {
  try {
    await deleteDoc(doc(db, 'players', playerId));
  } catch (error) {
    console.error('Error deleting player:', error);
    throw error;
  }
};

/**
 * Real-time listeners
 */
export const subscribeToGame = (gameId, callback) => {
  return onSnapshot(doc(db, 'games', gameId), (doc) => {
    if (doc.exists()) {
      callback({ id: doc.id, ...doc.data() });
    } else {
      callback(null);
    }
  });
};

export const subscribeToGamePlayers = (gameId, callback) => {
  const q = query(
    collection(db, 'players'), 
    where('gameId', '==', gameId),
    orderBy('position')
  );
  
  return onSnapshot(q, (querySnapshot) => {
    const players = querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    callback(players);
  });
};

export const subscribeToActiveGames = (callback) => {
  const q = query(
    collection(db, 'games'), 
    where('isActive', '==', true),
    orderBy('createdAt', 'desc')
  );
  
  return onSnapshot(q, (querySnapshot) => {
    const games = querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    callback(games);
  });
};

/**
 * Votes collection functions
 */
export const createVote = async (voteData) => {
  try {
    const docRef = await addDoc(collection(db, 'votes'), {
      ...voteData,
      createdAt: new Date().toISOString()
    });
    return { id: docRef.id, ...voteData };
  } catch (error) {
    console.error('Error creating vote:', error);
    throw error;
  }
};

export const getVotesByGame = async (gameId) => {
  try {
    const q = query(
      collection(db, 'votes'), 
      where('gameId', '==', gameId),
      orderBy('createdAt')
    );
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
  } catch (error) {
    console.error('Error getting votes by game:', error);
    return [];
  }
};

export const updateVote = async (voteId, updates) => {
  try {
    await updateDoc(doc(db, 'votes', voteId), updates);
  } catch (error) {
    console.error('Error updating vote:', error);
    throw error;
  }
};
