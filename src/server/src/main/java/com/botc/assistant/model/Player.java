package com.botc.assistant.model;

import com.botc.assistant.roles.Role;
import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

/**
 * Represents a player in a Blood on the Clock Tower game.
 * Contains player state, role assignment, and game-specific information.
 */
public class Player {

    private String id;

    @NotBlank
    @Size(min = 1, max = 50)
    private String name;

    private String userId; // Firebase UID of the user
    private User user; // Transient field for API responses

    private String gameId; // ID of the game this player belongs to

    private int position; // Seating position in the circle (0-based)

    private String roleId; // ID of the assigned role
    private Role role; // Transient field for API responses

    private boolean alive = true;
    private boolean canVote = true;
    private boolean nominated = false;
    private boolean hasVoted = false;

    private PlayerStatus status = PlayerStatus.ACTIVE;

    private List<String> neighbors = new ArrayList<>(); // IDs of neighboring players
    private String notes; // Storyteller notes about this player

    private LocalDateTime joinedAt;
    private LocalDateTime lastActivity;

    // Constructors
    public Player() {
        this.id = UUID.randomUUID().toString();
        this.joinedAt = LocalDateTime.now();
        this.lastActivity = LocalDateTime.now();
    }

    public Player(String name, int position, String gameId) {
        this();
        this.name = name;
        this.position = position;
        this.gameId = gameId;
    }

    public Player(User user, String name, int position, Game game) {
        this(name, position, game.getId());
        this.user = user;
        this.userId = user.getId();
    }

    // Business methods
    public void assignRole(Role role) {
        this.role = role;
        this.roleId = role.getId();
    }

    public void kill() {
        this.alive = false;
        this.canVote = false;
    }

    public void revive() {
        this.alive = true;
        this.canVote = true;
    }

    public void nominate() {
        this.nominated = true;
    }

    public void clearNomination() {
        this.nominated = false;
    }

    public void vote() {
        if (!canVote) {
            throw new IllegalStateException("Player cannot vote");
        }
        this.hasVoted = true;
    }

    public void resetVote() {
        this.hasVoted = false;
    }

    public void updateActivity() {
        this.lastActivity = LocalDateTime.now();
    }

    public boolean isNeighborOf(String playerId) {
        return neighbors.contains(playerId);
    }

    public void addNeighbor(String playerId) {
        if (!neighbors.contains(playerId)) {
            neighbors.add(playerId);
        }
    }

    public void removeNeighbor(String playerId) {
        neighbors.remove(playerId);
    }

    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
        if (user != null) {
            this.userId = user.getId();
        }
    }

    public String getGameId() {
        return gameId;
    }

    public void setGameId(String gameId) {
        this.gameId = gameId;
    }

    public int getPosition() {
        return position;
    }

    public void setPosition(int position) {
        this.position = position;
    }

    public String getRoleId() {
        return roleId;
    }

    public void setRoleId(String roleId) {
        this.roleId = roleId;
    }

    public Role getRole() {
        return role;
    }

    public void setRole(Role role) {
        this.role = role;
        if (role != null) {
            this.roleId = role.getId();
        }
    }

    public boolean isAlive() {
        return alive;
    }

    public void setAlive(boolean alive) {
        this.alive = alive;
        if (!alive) {
            this.canVote = false;
        }
    }

    public boolean canVote() {
        return canVote;
    }

    public void setCanVote(boolean canVote) {
        this.canVote = canVote;
    }

    public boolean isNominated() {
        return nominated;
    }

    public void setNominated(boolean nominated) {
        this.nominated = nominated;
    }

    public boolean hasVoted() {
        return hasVoted;
    }

    public void setHasVoted(boolean hasVoted) {
        this.hasVoted = hasVoted;
    }

    public PlayerStatus getStatus() {
        return status;
    }

    public void setStatus(PlayerStatus status) {
        this.status = status;
    }

    public List<String> getNeighbors() {
        return neighbors;
    }

    public void setNeighbors(List<String> neighbors) {
        this.neighbors = neighbors != null ? neighbors : new ArrayList<>();
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public LocalDateTime getJoinedAt() {
        return joinedAt;
    }

    public void setJoinedAt(LocalDateTime joinedAt) {
        this.joinedAt = joinedAt;
    }

    public LocalDateTime getLastActivity() {
        return lastActivity;
    }

    public void setLastActivity(LocalDateTime lastActivity) {
        this.lastActivity = lastActivity;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        Player player = (Player) obj;
        return Objects.equals(id, player.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public String toString() {
        return String.format("Player{id='%s', name='%s', position=%d, alive=%s, role='%s'}",
                id, name, position, alive, roleId);
    }

    /**
     * Player status in the game.
     */
    public enum PlayerStatus {
        ACTIVE,      // Actively participating
        DISCONNECTED, // Temporarily disconnected
        INACTIVE,    // Inactive but still in game
        ELIMINATED   // Eliminated from game
    }
}
