package com.botc.assistant.roles.minions;

/**
 * Spy - Minion role
 * 
 * Each night, you see the Grimoire. You might register as good & as a Townsfolk or Outsider, even if dead.
 * 
 * The Spy can see all information and confuse detection abilities.
 */
public class Spy extends Minion {
    
    public static final String ROLE_ID = "spy";
    public static final String ROLE_NAME = "Spy";
    public static final String ROLE_DESCRIPTION = "Each night, you see the Grimoire. You might register as good & as a Townsfolk or Outsider, even if dead.";
    
    private Spy(Builder builder) {
        super(builder);
    }
    
    public static Builder builder() {
        return new Builder();
    }
    
    public static Spy create() {
        return builder().build();
    }
    
    /**
     * Builder for Spy role
     */
    public static class Builder extends Minion.Builder<Builder> {
        
        public Builder() {
            id(ROLE_ID);
            name(ROLE_NAME);
            description(ROLE_DESCRIPTION);
            hasNightAbility(true);
            nightPriority(30); // Sees Grimoire each night
            setupInfo("Sees all game information and might register as good");
        }
        
        @Override
        protected Builder self() {
            return this;
        }
        
        @Override
        public Spy build() {
            return new Spy(this);
        }
    }
}
