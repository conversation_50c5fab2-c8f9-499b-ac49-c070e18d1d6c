name: Docker Cache Warmup

# This workflow pre-builds and caches Docker layers to speed up main builds
# Runs on dependency changes and weekly to keep cache fresh

on:
  # Run when dependencies change
  push:
    paths:
      - 'src/client/package*.json'
      - 'src/server/pom.xml'
      - 'Dockerfile*'
      - '.github/workflows/cache-warmup.yml'
  
  # Run weekly to refresh cache
  schedule:
    - cron: '0 2 * * 0'  # Every Sunday at 2 AM UTC
  
  # Allow manual triggering
  workflow_dispatch:

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  cache-warmup:
    name: Warm Docker Build Cache
    runs-on: ubuntu-latest
    
    permissions:
      contents: read
      packages: write
      
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Docker Buildx
      uses: docker/setup-buildx-action@v3
      with:
        buildkitd-config-inline: |
          [worker.oci]
            max-parallelism = 2
          [worker.containerd]
            max-parallelism = 2
      
    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Generate lowercase image name
      id: image-name
      run: echo "IMAGE_NAME_LOWER=$(echo '${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}' | tr '[:upper:]' '[:lower:]')" >> $GITHUB_OUTPUT

    # Warm up frontend dependencies cache
    - name: Build Frontend Dependencies Cache
      uses: docker/build-push-action@v5
      with:
        context: .
        target: frontend-deps
        platforms: linux/amd64
        push: false
        cache-from: |
          type=gha,scope=frontend-deps
          type=registry,ref=${{ steps.image-name.outputs.IMAGE_NAME_LOWER }}:cache-frontend-deps
        cache-to: |
          type=gha,scope=frontend-deps,mode=max
          type=registry,ref=${{ steps.image-name.outputs.IMAGE_NAME_LOWER }}:cache-frontend-deps,mode=max
        build-args: |
          BUILDKIT_INLINE_CACHE=1
    
    # Warm up backend dependencies cache
    - name: Build Backend Dependencies Cache
      uses: docker/build-push-action@v5
      with:
        context: .
        target: maven-deps
        platforms: linux/amd64
        push: false
        cache-from: |
          type=gha,scope=maven-deps
          type=registry,ref=${{ steps.image-name.outputs.IMAGE_NAME_LOWER }}:cache-maven-deps
        cache-to: |
          type=gha,scope=maven-deps,mode=max
          type=registry,ref=${{ steps.image-name.outputs.IMAGE_NAME_LOWER }}:cache-maven-deps,mode=max
        build-args: |
          BUILDKIT_INLINE_CACHE=1
    
    # Warm up full build cache
    - name: Build Full Application Cache
      uses: docker/build-push-action@v5
      with:
        context: .
        platforms: linux/amd64
        push: false
        cache-from: |
          type=gha
          type=registry,ref=${{ steps.image-name.outputs.IMAGE_NAME_LOWER }}:buildcache
        cache-to: |
          type=gha,mode=max
          type=registry,ref=${{ steps.image-name.outputs.IMAGE_NAME_LOWER }}:buildcache,mode=max
        build-args: |
          BUILDKIT_INLINE_CACHE=1
        provenance: false
        sbom: false
    
    # Clean up old cache entries
    - name: Cleanup Old Cache
      run: |
        # This helps prevent cache bloat by removing old entries
        echo "Cache warmup completed. Old cache entries will be automatically cleaned up by GitHub Actions."
