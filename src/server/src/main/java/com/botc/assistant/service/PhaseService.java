package com.botc.assistant.service;

import com.botc.assistant.model.Game;
import com.botc.assistant.model.Player;
import com.botc.assistant.repository.GameRepository;
import com.botc.assistant.repository.PlayerRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Service for managing game phases in Blood on the Clock Tower.
 * Handles phase transitions, timing, and phase-specific logic.
 */
@Service
public class PhaseService {

    private final GameRepository gameRepository;
    private final PlayerRepository playerRepository;
    private final NotificationService notificationService;

    @Autowired
    public PhaseService(GameRepository gameRepository, 
                       PlayerRepository playerRepository,
                       NotificationService notificationService) {
        this.gameRepository = gameRepository;
        this.playerRepository = playerRepository;
        this.notificationService = notificationService;
    }

    /**
     * Transition to the next phase in the game.
     */
    public Game transitionToNextPhase(Game game) {
        Game.GamePhase currentPhase = game.getCurrentPhase();
        Game.GamePhase nextPhase = determineNextPhase(currentPhase);
        
        // Perform phase-specific cleanup
        cleanupCurrentPhase(game, currentPhase);
        
        // Update game phase
        game.setCurrentPhase(nextPhase);
        
        // Perform phase-specific setup
        setupNewPhase(game, nextPhase);
        
        // Save game state
        game = gameRepository.save(game);
        
        // Send notification
        notificationService.sendPhaseChange(game.getId(), nextPhase.name(), game.getDayNumber());
        
        return game;
    }

    /**
     * Transition to a specific phase.
     */
    public Game transitionToPhase(Game game, Game.GamePhase targetPhase) {
        Game.GamePhase currentPhase = game.getCurrentPhase();
        
        // Perform phase-specific cleanup
        cleanupCurrentPhase(game, currentPhase);
        
        // Update game phase
        game.setCurrentPhase(targetPhase);
        
        // Update day number if transitioning to day
        if (targetPhase == Game.GamePhase.DAY && currentPhase == Game.GamePhase.NIGHT) {
            game.setDayNumber(game.getDayNumber() + 1);
        }
        
        // Perform phase-specific setup
        setupNewPhase(game, targetPhase);
        
        // Save game state
        game = gameRepository.save(game);
        
        // Send notification
        notificationService.sendPhaseChange(game.getId(), targetPhase.name(), game.getDayNumber());
        
        return game;
    }

    /**
     * Start the first night phase.
     */
    public Game startFirstNight(Game game) {
        if (game.getCurrentPhase() != Game.GamePhase.SETUP) {
            throw new IllegalStateException("Can only start first night from setup phase");
        }
        
        return transitionToPhase(game, Game.GamePhase.FIRST_NIGHT);
    }

    /**
     * Start a day phase.
     */
    public Game startDay(Game game) {
        if (game.getCurrentPhase() != Game.GamePhase.NIGHT && 
            game.getCurrentPhase() != Game.GamePhase.FIRST_NIGHT) {
            throw new IllegalStateException("Can only start day from night phase");
        }
        
        return transitionToPhase(game, Game.GamePhase.DAY);
    }

    /**
     * Start a night phase.
     */
    public Game startNight(Game game) {
        if (game.getCurrentPhase() != Game.GamePhase.DAY) {
            throw new IllegalStateException("Can only start night from day phase");
        }
        
        return transitionToPhase(game, Game.GamePhase.NIGHT);
    }

    /**
     * Start voting phase.
     */
    public Game startVoting(Game game) {
        if (game.getCurrentPhase() != Game.GamePhase.DAY) {
            throw new IllegalStateException("Can only start voting during day phase");
        }
        
        return transitionToPhase(game, Game.GamePhase.VOTING);
    }

    /**
     * End voting phase and return to day.
     */
    public Game endVoting(Game game) {
        if (game.getCurrentPhase() != Game.GamePhase.VOTING) {
            throw new IllegalStateException("Can only end voting during voting phase");
        }
        
        return transitionToPhase(game, Game.GamePhase.DAY);
    }

    /**
     * Check if the game should end based on win conditions.
     */
    public boolean checkWinConditions(Game game) {
        List<Player> alivePlayers = playerRepository.findAliveByGameId(game.getId());
        
        // Count alive players by team (simplified - would need role information)
        long aliveCount = alivePlayers.size();
        
        // Basic win condition: if 2 or fewer players remain, game should end
        if (aliveCount <= 2) {
            game.setState(Game.GameState.FINISHED);
            game.setCurrentPhase(Game.GamePhase.FINISHED);
            gameRepository.save(game);
            
            notificationService.sendGameEnd(game.getId(), "Unknown", "Too few players remaining");
            return true;
        }
        
        return false;
    }

    /**
     * Determine the next phase based on current phase.
     */
    private Game.GamePhase determineNextPhase(Game.GamePhase currentPhase) {
        return switch (currentPhase) {
            case SETUP -> Game.GamePhase.FIRST_NIGHT;
            case FIRST_NIGHT -> Game.GamePhase.DAY;
            case DAY -> Game.GamePhase.NIGHT;
            case NIGHT -> Game.GamePhase.DAY;
            case VOTING -> Game.GamePhase.DAY;
            case FINISHED -> Game.GamePhase.FINISHED;
        };
    }

    /**
     * Cleanup actions when leaving a phase.
     */
    private void cleanupCurrentPhase(Game game, Game.GamePhase phase) {
        switch (phase) {
            case DAY, VOTING -> {
                // Reset voting status for all players
                List<Player> players = playerRepository.findByGameId(game.getId());
                for (Player player : players) {
                    player.resetVote();
                    player.clearNomination();
                    playerRepository.save(player);
                }
            }
            case NIGHT, FIRST_NIGHT -> {
                // Reset night action status (if implemented)
                // This would reset any night ability usage flags
            }
            default -> {
                // No specific cleanup needed
            }
        }
    }

    /**
     * Setup actions when entering a new phase.
     */
    private void setupNewPhase(Game game, Game.GamePhase phase) {
        switch (phase) {
            case FIRST_NIGHT -> {
                // Initialize first night
                notificationService.sendSystemNotification(game.getId(), 
                    "First night begins. Storyteller, wake the appropriate roles.", "info");
            }
            case DAY -> {
                // Start day phase
                if (game.getDayNumber() == 1) {
                    notificationService.sendSystemNotification(game.getId(), 
                        "Day " + game.getDayNumber() + " begins. Players may now discuss and nominate.", "info");
                } else {
                    notificationService.sendSystemNotification(game.getId(), 
                        "Day " + game.getDayNumber() + " begins.", "info");
                }
            }
            case NIGHT -> {
                // Start night phase
                notificationService.sendSystemNotification(game.getId(), 
                    "Night falls. All players close your eyes.", "info");
            }
            case VOTING -> {
                // Start voting phase
                notificationService.sendSystemNotification(game.getId(), 
                    "Voting phase begins. Cast your votes now.", "info");
            }
            case FINISHED -> {
                // Game finished
                notificationService.sendSystemNotification(game.getId(), 
                    "Game has ended.", "info");
            }
            default -> {
                // No specific setup needed
            }
        }
    }

    /**
     * Get the current phase duration (for UI timers).
     */
    public long getPhaseTimeRemaining(Game game) {
        // This would calculate remaining time based on phase start time and duration
        // For now, return -1 to indicate no time limit
        return -1;
    }

    /**
     * Check if a phase transition is valid.
     */
    public boolean isValidPhaseTransition(Game.GamePhase from, Game.GamePhase to) {
        return switch (from) {
            case SETUP -> to == Game.GamePhase.FIRST_NIGHT;
            case FIRST_NIGHT -> to == Game.GamePhase.DAY;
            case DAY -> to == Game.GamePhase.NIGHT || to == Game.GamePhase.VOTING || to == Game.GamePhase.FINISHED;
            case NIGHT -> to == Game.GamePhase.DAY || to == Game.GamePhase.FINISHED;
            case VOTING -> to == Game.GamePhase.DAY;
            case FINISHED -> false; // No transitions from finished
        };
    }
}
