package com.botc.assistant.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.servlet.resource.PathResourceResolver;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

@Configuration
public class WebConfig implements WebMvcConfigurer {

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // Serve static resources from the React build
        registry.addResourceHandler("/**")
                .addResourceLocations("file:./static/", "classpath:/static/")
                .setCachePeriod(31536000)
                .resourceChain(true)
                .addResolver(new PathResourceResolver() {
                    @Override
                    protected Resource getResource(String resourcePath, Resource location) throws IOException {
                        Resource requestedResource = location.createRelative(resourcePath);

                        // If the requested resource exists, return it
                        if (requestedResource.exists() && requestedResource.isReadable()) {
                            return requestedResource;
                        }

                        // For client-side routing, return index.html for non-API requests
                        // and for paths that don't look like files
                        if (!resourcePath.startsWith("api/") && !resourcePath.contains(".")) {
                            // Try file system first
                            Path indexPath = Paths.get("static/index.html");
                            if (Files.exists(indexPath)) {
                                return new FileSystemResource(indexPath);
                            }
                            // Fallback to classpath
                            return new ClassPathResource("/static/index.html");
                        }

                        return null;
                    }
                });
    }
}
