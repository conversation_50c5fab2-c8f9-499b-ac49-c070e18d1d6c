package com.botc.assistant.roles.outsiders;

/**
 * <PERSON><PERSON><PERSON><PERSON> - Outsider role
 * 
 * You might register as evil & as a Minion or Demon, even if dead.
 * 
 * The Recluse can confuse information-gathering abilities.
 */
public class <PERSON><PERSON><PERSON><PERSON> extends Outsider {
    
    public static final String ROLE_ID = "recluse";
    public static final String ROLE_NAME = "Recluse";
    public static final String ROLE_DESCRIPTION = "You might register as evil & as a Minion or Demon, even if dead.";
    
    private Reclus<PERSON>(Builder builder) {
        super(builder);
    }
    
    public static Builder builder() {
        return new Builder();
    }
    
    public static Recluse create() {
        return builder().build();
    }
    
    /**
     * Builder for Recluse role
     */
    public static class Builder extends Outsider.Builder<Builder> {
        
        public Builder() {
            id(ROLE_ID);
            name(ROLE_NAME);
            description(ROLE_DESCRIPTION);
            setupInfo("Might register as evil to information-gathering abilities");
        }
        
        @Override
        protected Builder self() {
            return this;
        }
        
        @Override
        public Recluse build() {
            return new <PERSON><PERSON><PERSON><PERSON>(this);
        }
    }
}
