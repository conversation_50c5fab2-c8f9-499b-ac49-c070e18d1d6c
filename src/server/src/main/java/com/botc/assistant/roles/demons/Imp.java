package com.botc.assistant.roles.demons;

/**
 * Imp - Demon role
 * 
 * Each night*, choose a player: they die. If you kill yourself this way, a Minion becomes the Imp.
 * 
 * The Imp is the basic demon that kills each night.
 */
public class Imp extends Demon {
    
    public static final String ROLE_ID = "imp";
    public static final String ROLE_NAME = "Imp";
    public static final String ROLE_DESCRIPTION = "Each night*, choose a player: they die. If you kill yourself this way, a Minion becomes the Imp.";
    
    private Imp(Builder builder) {
        super(builder);
    }
    
    public static Builder builder() {
        return new Builder();
    }
    
    public static Imp create() {
        return builder().build();
    }
    
    /**
     * Builder for Imp role
     */
    public static class Builder extends Demon.Builder<Builder> {
        
        public Builder() {
            id(ROLE_ID);
            name(ROLE_NAME);
            description(ROLE_DESCRIPTION);
            nightPriority(50); // Demon kill
            setupInfo("Kills each night, can pass to <PERSON><PERSON> by self-kill");
        }
        
        @Override
        protected Builder self() {
            return this;
        }
        
        @Override
        public Imp build() {
            return new Imp(this);
        }
    }
}
