package com.botc.assistant.roles.townsfolks;

/**
 * Undertaker - Townsfolk role
 * 
 * Each night*, you learn which character died by execution today.
 * 
 * The Undertaker learns information about executed players.
 */
public class Undertaker extends TownsFolk {
    
    public static final String ROLE_ID = "undertaker";
    public static final String ROLE_NAME = "Undertaker";
    public static final String ROLE_DESCRIPTION = "Each night*, you learn which character died by execution today.";
    
    private Undertaker(Builder builder) {
        super(builder);
    }
    
    public static Builder builder() {
        return new Builder();
    }
    
    public static Undertaker create() {
        return builder().build();
    }
    
    /**
     * Builder for Undertaker role
     */
    public static class Builder extends TownsFolk.Builder<Builder> {
        
        public Builder() {
            id(ROLE_ID);
            name(ROLE_NAME);
            description(ROLE_DESCRIPTION);
            hasNightAbility(true);
            nightPriority(20); // Each night* (not first night)
            setupInfo("Learns which character died by execution each day");
        }
        
        @Override
        protected Builder self() {
            return this;
        }
        
        @Override
        public Undertaker build() {
            return new Undertaker(this);
        }
    }
}
