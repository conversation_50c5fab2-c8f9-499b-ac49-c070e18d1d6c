package com.botc.assistant.roles.townsfolks;

/**
 * Soldier - Townsfolk role
 * 
 * You are safe from the Demon.
 * 
 * The Soldier cannot be killed by the Demon at night.
 */
public class Soldier extends TownsFolk {
    
    public static final String ROLE_ID = "soldier";
    public static final String ROLE_NAME = "Soldier";
    public static final String ROLE_DESCRIPTION = "You are safe from the Demon.";
    
    private Soldier(Builder builder) {
        super(builder);
    }
    
    public static Builder builder() {
        return new Builder();
    }
    
    public static Soldier create() {
        return builder().build();
    }
    
    /**
     * Builder for Soldier role
     */
    public static class Builder extends TownsFolk.Builder<Builder> {
        
        public Builder() {
            id(ROLE_ID);
            name(ROLE_NAME);
            description(ROLE_DESCRIPTION);
            setupInfo("Cannot be killed by the Demon at night");
        }
        
        @Override
        protected Builder self() {
            return this;
        }
        
        @Override
        public Soldier build() {
            return new Soldier(this);
        }
    }
}
