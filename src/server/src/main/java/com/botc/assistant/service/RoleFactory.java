package com.botc.assistant.service;

import com.botc.assistant.roles.Role;
import com.botc.assistant.roles.townsfolks.*;
import com.botc.assistant.roles.outsiders.*;
import com.botc.assistant.roles.minions.*;
import com.botc.assistant.roles.demons.*;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * Factory for creating and managing role instances.
 * Provides centralized role creation and registration.
 */
@Component
public class RoleFactory {

    private final Map<String, Supplier<Role>> roleCreators;

    public RoleFactory() {
        this.roleCreators = initializeRoleCreators();
    }

    /**
     * Creates a role instance by ID.
     * 
     * @param roleId the role ID
     * @return the role instance, or null if not found
     */
    public Role createRole(String roleId) {
        Supplier<Role> creator = roleCreators.get(roleId);
        return creator != null ? creator.get() : null;
    }

    /**
     * Gets all available role IDs.
     * 
     * @return list of all role IDs
     */
    public List<String> getAllRoleIds() {
        return new ArrayList<>(roleCreators.keySet());
    }

    /**
     * Gets all available roles.
     * 
     * @return list of all role instances
     */
    public List<Role> getAllRoles() {
        return roleCreators.values().stream()
                .map(Supplier::get)
                .collect(Collectors.toList());
    }

    /**
     * Checks if a role ID is valid.
     * 
     * @param roleId the role ID to check
     * @return true if the role exists, false otherwise
     */
    public boolean isValidRoleId(String roleId) {
        return roleCreators.containsKey(roleId);
    }

    /**
     * Gets the total number of available roles.
     * 
     * @return the count of available roles
     */
    public int getRoleCount() {
        return roleCreators.size();
    }

    /**
     * Initializes the role creators map with all available roles.
     * 
     * @return map of role ID to role creator functions
     */
    private Map<String, Supplier<Role>> initializeRoleCreators() {
        Map<String, Supplier<Role>> creators = new HashMap<>();

        // Townsfolk roles
        creators.put(Chef.ROLE_ID, Chef::create);
        creators.put(Investigator.ROLE_ID, Investigator::create);
        creators.put(Librarian.ROLE_ID, Librarian::create);
        creators.put(Empath.ROLE_ID, Empath::create);
        creators.put(Monk.ROLE_ID, Monk::create);
        creators.put(Ravenkeeper.ROLE_ID, Ravenkeeper::create);
        creators.put(Washerwoman.ROLE_ID, Washerwoman::create);
        creators.put(Fortune_Teller.ROLE_ID, Fortune_Teller::create);
        creators.put(Undertaker.ROLE_ID, Undertaker::create);
        creators.put(Slayer.ROLE_ID, Slayer::create);
        creators.put(Soldier.ROLE_ID, Soldier::create);
        creators.put(Mayor.ROLE_ID, Mayor::create);
        creators.put(Virgin.ROLE_ID, Virgin::create);

        // Outsider roles
        creators.put(Butler.ROLE_ID, Butler::create);
        creators.put(Drunk.ROLE_ID, Drunk::create);
        creators.put(Recluse.ROLE_ID, Recluse::create);
        creators.put(Saint.ROLE_ID, Saint::create);

        // Minion roles
        creators.put(Poisoner.ROLE_ID, Poisoner::create);
        creators.put(Spy.ROLE_ID, Spy::create);
        creators.put(Scarlet_Woman.ROLE_ID, Scarlet_Woman::create);
        creators.put(Baron.ROLE_ID, Baron::create);

        // Demon roles
        creators.put(Imp.ROLE_ID, Imp::create);

        return creators;
    }
}
