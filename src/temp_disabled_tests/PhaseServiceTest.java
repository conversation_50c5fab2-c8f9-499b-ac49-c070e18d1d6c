package com.botc.assistant.service;

import com.botc.assistant.model.Game;
import com.botc.assistant.model.GamePhase;
import com.botc.assistant.model.Player;
import com.botc.assistant.model.User;
import com.botc.assistant.roles.Role;
import com.botc.assistant.roles.RoleTeam;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.lenient;

/**
 * Unit tests for PhaseService.
 */
@ExtendWith(MockitoExtension.class)
class PhaseServiceTest {

    @Mock
    private RoleService roleService;

    @Mock
    private NotificationService notificationService;

    @InjectMocks
    private PhaseService phaseService;

    private Game testGame;
    private Player testPlayer;
    private GamePhase setupPhase;
    private GamePhase firstNightPhase;
    private GamePhase dayPhase;
    private GamePhase nightPhase;
    private Role mockRole;

    @BeforeEach
    void setUp() {
        User storyteller = new User("storyteller", "<EMAIL>", "password");
        storyteller.setId("st-id");

        testGame = new Game("Test Game", 10, storyteller);
        testGame.setId("game-id");
        testGame.setState(Game.GameState.IN_PROGRESS);
        testGame.setDayNumber(1);

        testPlayer = new Player("Test Player", 0, testGame);
        testPlayer.setId("player-id");
        testPlayer.setAlive(true);
        testPlayer.setRoleId("test-role");

        testGame.getPlayers().add(testPlayer);

        setupPhase = new GamePhase(GamePhase.PhaseType.SETUP, 0, testGame);
        firstNightPhase = new GamePhase(GamePhase.PhaseType.FIRST_NIGHT, 0, testGame);
        dayPhase = new GamePhase(GamePhase.PhaseType.DAY, 1, testGame);
        nightPhase = new GamePhase(GamePhase.PhaseType.NIGHT, 1, testGame);

        mockRole = mock(Role.class);
        lenient().when(mockRole.getId()).thenReturn("test-role");
        lenient().when(mockRole.getName()).thenReturn("Test Role");
        lenient().when(mockRole.hasNightAbility()).thenReturn(true);
        lenient().when(mockRole.getNightPriority()).thenReturn(10);

        testPlayer.setRole(mockRole);
    }

    @Test
    void startFirstNight_ValidGame_ReturnsFirstNightPhase() {
        // Given
        testGame.setCurrentPhase(setupPhase);

        // When
        GamePhase result = phaseService.startFirstNight(testGame);

        // Then
        assertNotNull(result);
        assertEquals(GamePhase.PhaseType.FIRST_NIGHT, result.getType());
        assertEquals(0, result.getDayNumber());
        assertEquals(testGame, result.getGame());
        assertTrue(result.isActive());
        verify(notificationService).notifyPhaseChange(testGame, result);
    }

    @Test
    void startFirstNight_InvalidPhase_ThrowsException() {
        // Given
        testGame.setCurrentPhase(dayPhase);

        // When & Then
        IllegalStateException exception = assertThrows(IllegalStateException.class,
                () -> phaseService.startFirstNight(testGame));

        assertEquals("Cannot start first night - game not in setup phase", exception.getMessage());
        verify(notificationService, never()).notifyPhaseChange(any(Game.class), any(GamePhase.class));
    }

    @Test
    void startNightPhase_ValidGame_ReturnsNightPhase() {
        // When
        GamePhase result = phaseService.startNightPhase(testGame, 1);

        // Then
        assertNotNull(result);
        assertEquals(GamePhase.PhaseType.NIGHT, result.getType());
        assertEquals(1, result.getDayNumber());
        assertEquals(testGame, result.getGame());
        assertTrue(result.isActive());
        verify(notificationService).notifyPhaseChange(testGame, result);
    }

    @Test
    void startDayPhase_ValidGame_ReturnsDayPhase() {
        // When
        GamePhase result = phaseService.startDayPhase(testGame, 1);

        // Then
        assertNotNull(result);
        assertEquals(GamePhase.PhaseType.DAY, result.getType());
        assertEquals(1, result.getDayNumber());
        assertEquals(testGame, result.getGame());
        assertTrue(result.isActive());
        verify(notificationService).notifyPhaseChange(testGame, result);
    }

    @Test
    void startVotingPhase_ValidGame_ReturnsVotingPhase() {
        // Given
        testGame.setDayNumber(1);

        // When
        GamePhase result = phaseService.startVotingPhase(testGame);

        // Then
        assertNotNull(result);
        assertEquals(GamePhase.PhaseType.VOTING, result.getType());
        assertEquals(1, result.getDayNumber());
        assertEquals(testGame, result.getGame());
        assertTrue(result.isActive());
        verify(notificationService).notifyPhaseChange(testGame, result);
    }

    @Test
    void transitionToNextPhase_FromSetup_ReturnsFirstNight() {
        // Given
        testGame.setCurrentPhase(setupPhase);

        // When
        GamePhase result = phaseService.transitionToNextPhase(testGame);

        // Then
        assertNotNull(result);
        assertEquals(GamePhase.PhaseType.FIRST_NIGHT, result.getType());
        assertFalse(setupPhase.isActive());
        verify(notificationService).notifyPhaseChange(testGame, result);
    }

    @Test
    void transitionToNextPhase_FromFirstNight_ReturnsDay() {
        // Given
        testGame.setCurrentPhase(firstNightPhase);
        firstNightPhase.startPhase();

        // When
        GamePhase result = phaseService.transitionToNextPhase(testGame);

        // Then
        assertNotNull(result);
        assertEquals(GamePhase.PhaseType.DAY, result.getType());
        assertEquals(1, result.getDayNumber());
        assertFalse(firstNightPhase.isActive());
        verify(notificationService).notifyPhaseChange(testGame, result);
    }

    @Test
    void transitionToNextPhase_FromDay_ReturnsVoting() {
        // Given
        testGame.setCurrentPhase(dayPhase);
        dayPhase.startPhase();

        // When
        GamePhase result = phaseService.transitionToNextPhase(testGame);

        // Then
        assertNotNull(result);
        assertEquals(GamePhase.PhaseType.VOTING, result.getType());
        assertEquals(1, result.getDayNumber());
        assertFalse(dayPhase.isActive());
        verify(notificationService).notifyPhaseChange(testGame, result);
    }

    @Test
    void transitionToNextPhase_FromVoting_ReturnsNight() {
        // Given
        GamePhase votingPhase = new GamePhase(GamePhase.PhaseType.VOTING, 1, testGame);
        testGame.setCurrentPhase(votingPhase);
        votingPhase.startPhase();

        // When
        GamePhase result = phaseService.transitionToNextPhase(testGame);

        // Then
        assertNotNull(result);
        assertEquals(GamePhase.PhaseType.NIGHT, result.getType());
        assertEquals(1, result.getDayNumber());
        assertFalse(votingPhase.isActive());
        verify(notificationService).notifyPhaseChange(testGame, result);
    }

    @Test
    void transitionToNextPhase_FromNight_ReturnsDay() {
        // Given
        testGame.setCurrentPhase(nightPhase);
        nightPhase.startPhase();

        // When
        GamePhase result = phaseService.transitionToNextPhase(testGame);

        // Then
        assertNotNull(result);
        assertEquals(GamePhase.PhaseType.DAY, result.getType());
        assertEquals(2, result.getDayNumber()); // Should increment day number
        assertFalse(nightPhase.isActive());
        verify(notificationService).notifyPhaseChange(testGame, result);
    }

    @Test
    void transitionToNextPhase_NoCurrentPhase_ThrowsException() {
        // Given
        testGame.setCurrentPhase(null);

        // When & Then
        IllegalStateException exception = assertThrows(IllegalStateException.class,
                () -> phaseService.transitionToNextPhase(testGame));

        assertEquals("No current phase to transition from", exception.getMessage());
        verify(notificationService, never()).notifyPhaseChange(any(Game.class), any(GamePhase.class));
    }

    @Test
    void processNightAction_ValidAction_ProcessesAction() {
        // Given
        testGame.setCurrentPhase(nightPhase);
        nightPhase.startPhase();
        nightPhase.setActiveRoleIds(new ArrayList<>(Arrays.asList("test-role")));

        when(roleService.getRoleById("test-role")).thenReturn(Optional.of(mockRole));

        // When
        phaseService.processNightAction(testGame, "test-role", "action-data");

        // Then
        verify(roleService).getRoleById("test-role");
        assertFalse(nightPhase.hasActiveRole("test-role"));
    }

    @Test
    void processNightAction_NotNightPhase_ThrowsException() {
        // Given
        testGame.setCurrentPhase(dayPhase);

        // When & Then
        IllegalStateException exception = assertThrows(IllegalStateException.class,
                () -> phaseService.processNightAction(testGame, "test-role", "action-data"));

        assertEquals("Cannot process night action during non-night phase", exception.getMessage());
        verify(roleService, never()).getRoleById(anyString());
    }

    @Test
    void processNightAction_RoleNotActive_ThrowsException() {
        // Given
        testGame.setCurrentPhase(nightPhase);
        nightPhase.startPhase();
        nightPhase.setActiveRoleIds(new ArrayList<>(Arrays.asList("other-role")));

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
                () -> phaseService.processNightAction(testGame, "test-role", "action-data"));

        assertEquals("Role is not active during this phase: test-role", exception.getMessage());
        verify(roleService, never()).getRoleById(anyString());
    }

    @Test
    void processNightAction_RoleNotFound_ThrowsException() {
        // Given
        testGame.setCurrentPhase(nightPhase);
        nightPhase.startPhase();
        nightPhase.setActiveRoleIds(new ArrayList<>(Arrays.asList("test-role")));

        when(roleService.getRoleById("test-role")).thenReturn(Optional.empty());

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
                () -> phaseService.processNightAction(testGame, "test-role", "action-data"));

        assertEquals("Role not found: test-role", exception.getMessage());
        verify(roleService).getRoleById("test-role");
    }

    @Test
    void pausePhase_ActivePhase_PausesPhase() {
        // Given
        testGame.setCurrentPhase(dayPhase);
        dayPhase.startPhase();

        // When
        phaseService.pausePhase(testGame);

        // Then
        assertTrue(dayPhase.isPaused());
        verify(notificationService).notifyPhasePaused(testGame);
    }

    @Test
    void pausePhase_NoCurrentPhase_DoesNothing() {
        // Given
        testGame.setCurrentPhase(null);

        // When
        phaseService.pausePhase(testGame);

        // Then
        verify(notificationService, never()).notifyPhasePaused(any(Game.class));
    }

    @Test
    void resumePhase_PausedPhase_ResumesPhase() {
        // Given
        testGame.setCurrentPhase(dayPhase);
        dayPhase.startPhase();
        dayPhase.pausePhase();

        // When
        phaseService.resumePhase(testGame);

        // Then
        assertFalse(dayPhase.isPaused());
        verify(notificationService).notifyPhaseResumed(testGame);
    }

    @Test
    void resumePhase_NotPausedPhase_DoesNothing() {
        // Given
        testGame.setCurrentPhase(dayPhase);
        dayPhase.startPhase();

        // When
        phaseService.resumePhase(testGame);

        // Then
        verify(notificationService, never()).notifyPhaseResumed(any(Game.class));
    }

    @Test
    void checkWinConditions_NoEvilLeft_ReturnsTrue() {
        // Given
        when(mockRole.getTeam()).thenReturn(RoleTeam.GOOD);
        testPlayer.setRole(mockRole);

        // When
        boolean result = phaseService.checkWinConditions(testGame);

        // Then
        assertTrue(result);
        assertEquals(Game.GameState.GOOD_WINS, testGame.getState());
        verify(notificationService).notifyGameEnd(testGame, Game.GameState.GOOD_WINS);
    }

    @Test
    void checkWinConditions_EvilEqualsGood_ReturnsTrue() {
        // Given
        Player evilPlayer = new Player("Evil Player", 1, testGame);
        evilPlayer.setAlive(true);
        Role evilRole = mock(Role.class);
        when(evilRole.getTeam()).thenReturn(RoleTeam.EVIL);
        evilPlayer.setRole(evilRole);
        testGame.getPlayers().add(evilPlayer);

        when(mockRole.getTeam()).thenReturn(RoleTeam.GOOD);
        testPlayer.setRole(mockRole);

        // When
        boolean result = phaseService.checkWinConditions(testGame);

        // Then
        assertTrue(result);
        assertEquals(Game.GameState.EVIL_WINS, testGame.getState());
        verify(notificationService).notifyGameEnd(testGame, Game.GameState.EVIL_WINS);
    }

    @Test
    void checkWinConditions_GameContinues_ReturnsFalse() {
        // Given
        Player goodPlayer1 = new Player("Good Player 1", 1, testGame);
        goodPlayer1.setAlive(true);
        Role goodRole1 = mock(Role.class);
        when(goodRole1.getTeam()).thenReturn(RoleTeam.GOOD);
        goodPlayer1.setRole(goodRole1);

        Player goodPlayer2 = new Player("Good Player 2", 2, testGame);
        goodPlayer2.setAlive(true);
        Role goodRole2 = mock(Role.class);
        when(goodRole2.getTeam()).thenReturn(RoleTeam.GOOD);
        goodPlayer2.setRole(goodRole2);

        Player evilPlayer = new Player("Evil Player", 3, testGame);
        evilPlayer.setAlive(true);
        Role evilRole = mock(Role.class);
        when(evilRole.getTeam()).thenReturn(RoleTeam.EVIL);
        evilPlayer.setRole(evilRole);

        testGame.getPlayers().addAll(Arrays.asList(goodPlayer1, goodPlayer2, evilPlayer));

        when(mockRole.getTeam()).thenReturn(RoleTeam.GOOD);
        testPlayer.setRole(mockRole);

        // When
        boolean result = phaseService.checkWinConditions(testGame);

        // Then
        assertFalse(result);
        assertEquals(Game.GameState.IN_PROGRESS, testGame.getState());
        verify(notificationService, never()).notifyGameEnd(any(Game.class), any(Game.GameState.class));
    }
}
