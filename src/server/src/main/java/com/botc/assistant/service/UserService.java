package com.botc.assistant.service;

import com.botc.assistant.model.User;
import com.google.cloud.firestore.DocumentReference;
import com.google.cloud.firestore.DocumentSnapshot;
import com.google.cloud.firestore.Firestore;
import com.google.cloud.firestore.QueryDocumentSnapshot;
import com.google.firebase.auth.FirebaseAuth;
import com.google.firebase.auth.FirebaseAuthException;
import com.google.firebase.auth.FirebaseToken;
import com.google.firebase.auth.UserRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ExecutionException;

/**
 * Firebase-based user service for authentication and user management.
 * Handles user registration, authentication, and profile management using
 * Firebase Auth.
 */
@Service
public class UserService implements UserDetailsService {

    private final FirebaseAuth firebaseAuth;
    private final Firestore firestore;

    @Autowired
    public UserService(@Autowired(required = false) FirebaseAuth firebaseAuth,
            @Autowired(required = false) Firestore firestore) {
        this.firebaseAuth = firebaseAuth;
        this.firestore = firestore;
    }

    /**
     * Register a new user with Firebase Authentication.
     */
    public User registerUser(String username, String email, String password) {
        if (firebaseAuth == null) {
            throw new RuntimeException("Firebase Authentication is not properly configured");
        }
        if (firestore == null) {
            throw new RuntimeException("Firestore is not properly configured");
        }

        try {
            // Check if user already exists by email
            try {
                firebaseAuth.getUserByEmail(email);
                throw new IllegalArgumentException("Email already exists: " + email);
            } catch (FirebaseAuthException e) {
                // User doesn't exist, which is what we want
            }

            // Create user in Firebase
            UserRecord.CreateRequest request = new UserRecord.CreateRequest()
                    .setEmail(email)
                    .setPassword(password)
                    .setDisplayName(username)
                    .setEmailVerified(false);

            UserRecord userRecord = firebaseAuth.createUser(request);

            // Set custom claims for user role and statistics
            Map<String, Object> claims = new HashMap<>();
            claims.put("role", User.UserRole.PLAYER.name());
            claims.put("gamesPlayed", 0);
            claims.put("gamesWon", 0);
            claims.put("gamesAsStoryteller", 0);
            claims.put("username", username);

            firebaseAuth.setCustomUserClaims(userRecord.getUid(), claims);

            // Create User object
            User user = new User(userRecord.getUid(), username, email);
            user.setUserRole(User.UserRole.PLAYER);
            user.setCreatedAt(LocalDateTime.now());
            user.setLastLoginAt(LocalDateTime.now());

            // Store user data in Firestore
            Map<String, Object> userData = new HashMap<>();
            userData.put("id", user.getId());
            userData.put("username", user.getUsername());
            userData.put("email", user.getEmail());
            userData.put("userRole", user.getUserRole().name());
            userData.put("gamesPlayed", user.getGamesPlayed());
            userData.put("gamesWon", user.getGamesWon());
            userData.put("gamesAsStoryteller", user.getGamesAsStoryteller());
            userData.put("createdAt", user.getCreatedAt().toString());
            userData.put("lastLoginAt", user.getLastLoginAt().toString());
            userData.put("enabled", user.isEnabled());

            firestore.collection("users").document(userRecord.getUid()).set(userData);

            return user;

        } catch (FirebaseAuthException e) {
            throw new RuntimeException("Failed to register user: " + e.getMessage(), e);
        }
    }

    /**
     * Register a new storyteller.
     */
    public User registerStoryteller(String username, String email, String password) {
        User user = registerUser(username, email, password);
        if (firebaseAuth != null) {
            promoteToStoryteller(user.getId());
        } else {
            user.setUserRole(User.UserRole.STORYTELLER);
        }
        return user;
    }

    /**
     * Find user by Firebase UID.
     */
    public Optional<User> findById(String uid) {
        if (firestore == null) {
            return Optional.empty();
        }

        try {
            var document = firestore.collection("users").document(uid).get().get();
            if (document.exists()) {
                User user = documentToUser(document);
                return Optional.ofNullable(user);
            }
            return Optional.empty();
        } catch (InterruptedException | ExecutionException e) {
            System.err.println("Error finding user by ID: " + e.getMessage());
            return Optional.empty();
        }
    }

    /**
     * Find user by email.
     */
    public Optional<User> findByEmail(String email) {
        if (firestore == null) {
            return Optional.empty();
        }

        try {
            var query = firestore.collection("users").whereEqualTo("email", email).get().get();
            if (!query.isEmpty()) {
                var document = query.getDocuments().get(0);
                User user = documentToUser(document);
                return Optional.ofNullable(user);
            }
            return Optional.empty();
        } catch (InterruptedException | ExecutionException e) {
            System.err.println("Error finding user by email: " + e.getMessage());
            return Optional.empty();
        }
    }

    /**
     * Verify Firebase ID token and return user.
     */
    public User verifyIdToken(String idToken) throws FirebaseAuthException {
        if (firebaseAuth == null) {
            throw new RuntimeException("Firebase not configured");
        }

        FirebaseToken decodedToken = firebaseAuth.verifyIdToken(idToken);
        String uid = decodedToken.getUid();

        // Get user from Firestore
        Optional<User> userOpt = findById(uid);
        if (userOpt.isPresent()) {
            return userOpt.get();
        }

        // If user not found in Firestore, create from Firebase Auth record
        UserRecord userRecord = firebaseAuth.getUser(uid);
        return createUserFromFirebaseRecord(userRecord);
    }

    /**
     * Promote user to storyteller role.
     */
    public void promoteToStoryteller(String uid) {
        if (firebaseAuth == null) {
            return;
        }

        try {
            UserRecord userRecord = firebaseAuth.getUser(uid);
            Map<String, Object> claims = userRecord.getCustomClaims();
            if (claims == null) {
                claims = new HashMap<>();
            }
            claims.put("role", User.UserRole.STORYTELLER.name());
            firebaseAuth.setCustomUserClaims(uid, claims);
        } catch (FirebaseAuthException e) {
            throw new RuntimeException("Failed to promote user to storyteller: " + e.getMessage(), e);
        }
    }

    /**
     * Update user statistics after a game.
     */
    public void recordGamePlayed(String uid, boolean won, boolean asStoryteller) {
        if (firebaseAuth == null) {
            return;
        }

        try {
            UserRecord userRecord = firebaseAuth.getUser(uid);
            Map<String, Object> claims = userRecord.getCustomClaims();
            if (claims == null) {
                claims = new HashMap<>();
            }

            int gamesPlayed = (Integer) claims.getOrDefault("gamesPlayed", 0) + 1;
            int gamesWon = (Integer) claims.getOrDefault("gamesWon", 0) + (won ? 1 : 0);
            int gamesAsStoryteller = (Integer) claims.getOrDefault("gamesAsStoryteller", 0) + (asStoryteller ? 1 : 0);

            claims.put("gamesPlayed", gamesPlayed);
            claims.put("gamesWon", gamesWon);
            claims.put("gamesAsStoryteller", gamesAsStoryteller);

            firebaseAuth.setCustomUserClaims(uid, claims);
        } catch (FirebaseAuthException e) {
            throw new RuntimeException("Failed to update user statistics: " + e.getMessage(), e);
        }
    }

    /**
     * Check if username is available (simplified - in real implementation would
     * check Firestore).
     */
    public boolean isUsernameAvailable(String username) {
        // In a real implementation, you'd check Firestore for username uniqueness
        // For now, we'll assume it's available
        return true;
    }

    /**
     * Check if email is available.
     */
    public boolean isEmailAvailable(String email) {
        if (firebaseAuth == null) {
            return true; // For demo
        }

        try {
            firebaseAuth.getUserByEmail(email);
            return false; // Email exists
        } catch (FirebaseAuthException e) {
            return true; // Email doesn't exist
        }
    }

    /**
     * UserDetailsService implementation for Spring Security.
     */
    @Override
    public UserDetails loadUserByUsername(String email) throws UsernameNotFoundException {
        return findByEmail(email)
                .orElseThrow(() -> new UsernameNotFoundException("User not found: " + email));
    }

    /**
     * Create User object from Firebase UserRecord.
     */
    private User createUserFromFirebaseRecord(UserRecord userRecord) {
        Map<String, Object> claims = userRecord.getCustomClaims();

        String username = (String) (claims != null ? claims.get("username") : userRecord.getDisplayName());
        if (username == null) {
            username = userRecord.getEmail().split("@")[0]; // Fallback to email prefix
        }

        User user = new User(userRecord.getUid(), username, userRecord.getEmail());

        if (claims != null) {
            String roleStr = (String) claims.get("role");
            if (roleStr != null) {
                user.setUserRole(User.UserRole.valueOf(roleStr));
            }

            user.setGamesPlayed((Integer) claims.getOrDefault("gamesPlayed", 0));
            user.setGamesWon((Integer) claims.getOrDefault("gamesWon", 0));
            user.setGamesAsStoryteller((Integer) claims.getOrDefault("gamesAsStoryteller", 0));
        }

        user.setEnabled(!userRecord.isDisabled());
        user.setCreatedAt(LocalDateTime.ofInstant(
                java.time.Instant.ofEpochMilli(userRecord.getUserMetadata().getCreationTimestamp()),
                java.time.ZoneId.systemDefault()));

        return user;
    }

    /**
     * Update user's last login time.
     */
    public void updateLastLogin(String uid) {
        if (firestore == null) {
            return;
        }

        try {
            Map<String, Object> updates = new HashMap<>();
            updates.put("lastLoginAt", LocalDateTime.now().toString());
            firestore.collection("users").document(uid).update(updates);
        } catch (Exception e) {
            System.err.println("Error updating last login time: " + e.getMessage());
        }
    }

    /**
     * Update user profile information.
     */
    public void updateProfile(String uid, String newUsername) {
        if (firebaseAuth == null) {
            return; // For demo
        }

        try {
            UserRecord userRecord = firebaseAuth.getUser(uid);
            Map<String, Object> claims = userRecord.getCustomClaims();
            if (claims == null) {
                claims = new HashMap<>();
            }
            claims.put("username", newUsername);
            firebaseAuth.setCustomUserClaims(uid, claims);
        } catch (FirebaseAuthException e) {
            throw new RuntimeException("Failed to update user profile: " + e.getMessage(), e);
        }
    }

    /**
     * Change user password.
     */
    public void changePassword(String uid, String oldPassword, String newPassword) {
        if (firebaseAuth == null) {
            return; // For demo
        }

        try {
            UserRecord.UpdateRequest request = new UserRecord.UpdateRequest(uid)
                    .setPassword(newPassword);
            firebaseAuth.updateUser(request);
        } catch (FirebaseAuthException e) {
            throw new RuntimeException("Failed to change password: " + e.getMessage(), e);
        }
    }

    /**
     * Change user role.
     */
    public void changeUserRole(String uid, User.UserRole newRole) {
        if (firebaseAuth == null || firestore == null) {
            return;
        }

        try {
            // Update Firebase Auth custom claims
            UserRecord userRecord = firebaseAuth.getUser(uid);
            Map<String, Object> claims = userRecord.getCustomClaims();
            if (claims == null) {
                claims = new HashMap<>();
            }
            claims.put("role", newRole.name());
            firebaseAuth.setCustomUserClaims(uid, claims);

            // Update Firestore user document
            Map<String, Object> updates = new HashMap<>();
            updates.put("userRole", newRole.name());
            firestore.collection("users").document(uid).update(updates);

        } catch (FirebaseAuthException e) {
            throw new RuntimeException("Failed to change user role: " + e.getMessage(), e);
        }
    }

    /**
     * Get list of active users from Firestore.
     */
    public List<User> getActiveUsers() {
        if (firestore == null) {
            return new ArrayList<>();
        }

        try {
            List<User> users = new ArrayList<>();
            var documents = firestore.collection("users").get().get();

            for (var document : documents.getDocuments()) {
                if (document.exists()) {
                    User user = documentToUser(document);
                    if (user != null && user.isEnabled()) {
                        users.add(user);
                    }
                }
            }

            return users;
        } catch (InterruptedException | ExecutionException e) {
            System.err.println("Error retrieving users from Firestore: " + e.getMessage());
            return new ArrayList<>();
        }
    }

    /**
     * Convert Firestore document to User object.
     */
    private User documentToUser(DocumentSnapshot document) {
        try {
            String id = document.getString("id");
            String username = document.getString("username");
            String email = document.getString("email");

            if (id == null || username == null || email == null) {
                return null;
            }

            User user = new User(id, username, email);

            String roleStr = document.getString("userRole");
            if (roleStr != null) {
                user.setUserRole(User.UserRole.valueOf(roleStr));
            }

            Long gamesPlayed = document.getLong("gamesPlayed");
            if (gamesPlayed != null) {
                user.setGamesPlayed(gamesPlayed.intValue());
            }

            Long gamesWon = document.getLong("gamesWon");
            if (gamesWon != null) {
                user.setGamesWon(gamesWon.intValue());
            }

            Long gamesAsStoryteller = document.getLong("gamesAsStoryteller");
            if (gamesAsStoryteller != null) {
                user.setGamesAsStoryteller(gamesAsStoryteller.intValue());
            }

            Boolean enabled = document.getBoolean("enabled");
            if (enabled != null) {
                user.setEnabled(enabled);
            }

            return user;
        } catch (Exception e) {
            System.err.println("Error converting document to user: " + e.getMessage());
            return null;
        }
    }

}
