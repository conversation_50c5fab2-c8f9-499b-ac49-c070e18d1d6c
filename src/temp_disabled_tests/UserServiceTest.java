package com.botc.assistant.service;

import com.botc.assistant.model.User;
import com.botc.assistant.repository.UserRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.password.PasswordEncoder;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for UserService.
 */
@ExtendWith(MockitoExtension.class)
class UserServiceTest {

    @Mock
    private UserRepository userRepository;

    @Mock
    private PasswordEncoder passwordEncoder;

    @InjectMocks
    private UserService userService;

    private User testUser;
    private User testStoryteller;

    @BeforeEach
    void setUp() {
        testUser = new User("testuser", "<EMAIL>", "encodedPassword");
        testUser.setId("user-id-123");
        testUser.setUserRole(User.UserRole.PLAYER);
        testUser.setEnabled(true);
        testUser.setCreatedAt(LocalDateTime.now().minusDays(1));

        testStoryteller = new User("storyteller", "<EMAIL>", "encodedPassword");
        testStoryteller.setId("st-id-456");
        testStoryteller.setUserRole(User.UserRole.STORYTELLER);
        testStoryteller.setEnabled(true);
    }

    @Test
    void loadUserByUsername_ExistingUser_ReturnsUserDetails() {
        // Given
        when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(testUser));

        // When
        UserDetails result = userService.loadUserByUsername("testuser");

        // Then
        assertNotNull(result);
        assertEquals("testuser", result.getUsername());
        assertTrue(result.isEnabled());
        verify(userRepository).findByUsername("testuser");
    }

    @Test
    void loadUserByUsername_NonExistentUser_ThrowsException() {
        // Given
        when(userRepository.findByUsername("nonexistent")).thenReturn(Optional.empty());

        // When & Then
        assertThrows(UsernameNotFoundException.class,
                () -> userService.loadUserByUsername("nonexistent"));
        verify(userRepository).findByUsername("nonexistent");
    }

    @Test
    void registerUser_ValidData_ReturnsUser() {
        // Given
        when(userRepository.existsByUsername("newuser")).thenReturn(false);
        when(userRepository.existsByEmail("<EMAIL>")).thenReturn(false);
        when(passwordEncoder.encode("password")).thenReturn("encodedPassword");
        when(userRepository.save(any(User.class))).thenReturn(testUser);

        // When
        User result = userService.registerUser("newuser", "<EMAIL>", "password");

        // Then
        assertNotNull(result);
        verify(userRepository).existsByUsername("newuser");
        verify(userRepository).existsByEmail("<EMAIL>");
        verify(passwordEncoder).encode("password");
        verify(userRepository).save(any(User.class));
    }

    @Test
    void registerUser_ExistingUsername_ThrowsException() {
        // Given
        when(userRepository.existsByUsername("existinguser")).thenReturn(true);

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
                () -> userService.registerUser("existinguser", "<EMAIL>", "password"));

        assertEquals("Username already exists: existinguser", exception.getMessage());
        verify(userRepository).existsByUsername("existinguser");
        verify(userRepository, never()).save(any(User.class));
    }

    @Test
    void registerUser_ExistingEmail_ThrowsException() {
        // Given
        when(userRepository.existsByUsername("newuser")).thenReturn(false);
        when(userRepository.existsByEmail("<EMAIL>")).thenReturn(true);

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
                () -> userService.registerUser("newuser", "<EMAIL>", "password"));

        assertEquals("Email already exists: <EMAIL>", exception.getMessage());
        verify(userRepository).existsByEmail("<EMAIL>");
        verify(userRepository, never()).save(any(User.class));
    }

    @Test
    void registerStoryteller_ValidData_ReturnsStoryteller() {
        // Given
        when(userRepository.existsByUsername("newst")).thenReturn(false);
        when(userRepository.existsByEmail("<EMAIL>")).thenReturn(false);
        when(passwordEncoder.encode("password")).thenReturn("encodedPassword");
        when(userRepository.save(any(User.class))).thenReturn(testStoryteller);

        // When
        User result = userService.registerStoryteller("newst", "<EMAIL>", "password");

        // Then
        assertNotNull(result);
        assertEquals(User.UserRole.STORYTELLER, result.getUserRole());
        verify(userRepository, times(2)).save(any(User.class)); // Called twice: once in registerUser, once in
                                                                // registerStoryteller
    }

    @Test
    void findById_ExistingUser_ReturnsUser() {
        // Given
        when(userRepository.findById("user-id-123")).thenReturn(Optional.of(testUser));

        // When
        Optional<User> result = userService.findById("user-id-123");

        // Then
        assertTrue(result.isPresent());
        assertEquals(testUser, result.get());
        verify(userRepository).findById("user-id-123");
    }

    @Test
    void findById_NonExistentUser_ReturnsEmpty() {
        // Given
        when(userRepository.findById("nonexistent")).thenReturn(Optional.empty());

        // When
        Optional<User> result = userService.findById("nonexistent");

        // Then
        assertFalse(result.isPresent());
        verify(userRepository).findById("nonexistent");
    }

    @Test
    void updateProfile_ValidData_ReturnsUpdatedUser() {
        // Given
        User updatedUser = new User("testuser", "<EMAIL>", "encodedPassword");
        updatedUser.setId("user-id-123");

        when(userRepository.findById("user-id-123")).thenReturn(Optional.of(testUser));
        when(userRepository.existsByEmail("<EMAIL>")).thenReturn(false);
        when(userRepository.save(any(User.class))).thenReturn(updatedUser);

        // When
        User result = userService.updateProfile("user-id-123", "<EMAIL>");

        // Then
        assertNotNull(result);
        verify(userRepository).findById("user-id-123");
        verify(userRepository).existsByEmail("<EMAIL>");
        verify(userRepository).save(testUser);
    }

    @Test
    void updateProfile_ExistingEmail_ThrowsException() {
        // Given
        when(userRepository.findById("user-id-123")).thenReturn(Optional.of(testUser));
        when(userRepository.existsByEmail("<EMAIL>")).thenReturn(true);

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
                () -> userService.updateProfile("user-id-123", "<EMAIL>"));

        assertEquals("Email already exists: <EMAIL>", exception.getMessage());
        verify(userRepository, never()).save(any(User.class));
    }

    @Test
    void changePassword_ValidData_UpdatesPassword() {
        // Given
        when(userRepository.findById("user-id-123")).thenReturn(Optional.of(testUser));
        when(passwordEncoder.matches("oldpassword", "encodedPassword")).thenReturn(true);
        when(passwordEncoder.encode("newpassword")).thenReturn("newEncodedPassword");

        // When
        userService.changePassword("user-id-123", "oldpassword", "newpassword");

        // Then
        verify(userRepository).findById("user-id-123");
        verify(passwordEncoder).matches("oldpassword", "encodedPassword");
        verify(passwordEncoder).encode("newpassword");
        verify(userRepository).save(testUser);
    }

    @Test
    void changePassword_InvalidOldPassword_ThrowsException() {
        // Given
        when(userRepository.findById("user-id-123")).thenReturn(Optional.of(testUser));
        when(passwordEncoder.matches("wrongpassword", "encodedPassword")).thenReturn(false);

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
                () -> userService.changePassword("user-id-123", "wrongpassword", "newpassword"));

        assertEquals("Invalid old password", exception.getMessage());
        verify(userRepository, never()).save(any(User.class));
    }

    @Test
    void updateLastLogin_ValidUser_UpdatesTimestamp() {
        // Given
        when(userRepository.findById("user-id-123")).thenReturn(Optional.of(testUser));

        // When
        userService.updateLastLogin("user-id-123");

        // Then
        verify(userRepository).findById("user-id-123");
        verify(userRepository).save(testUser);
        assertNotNull(testUser.getLastLoginAt());
    }

    @Test
    void incrementGamesPlayed_ValidUser_IncrementsCount() {
        // Given
        int initialCount = testUser.getGamesPlayed();
        when(userRepository.findById("user-id-123")).thenReturn(Optional.of(testUser));

        // When
        userService.incrementGamesPlayed("user-id-123");

        // Then
        verify(userRepository).findById("user-id-123");
        verify(userRepository).save(testUser);
        assertEquals(initialCount + 1, testUser.getGamesPlayed());
    }

    @Test
    void incrementGamesWon_ValidUser_IncrementsCount() {
        // Given
        int initialCount = testUser.getGamesWon();
        when(userRepository.findById("user-id-123")).thenReturn(Optional.of(testUser));

        // When
        userService.incrementGamesWon("user-id-123");

        // Then
        verify(userRepository).findById("user-id-123");
        verify(userRepository).save(testUser);
        assertEquals(initialCount + 1, testUser.getGamesWon());
    }

    @Test
    void getAllStorytellers_ReturnsStorytellers() {
        // Given
        List<User> storytellers = Arrays.asList(testStoryteller);
        when(userRepository.findAllStorytellers()).thenReturn(storytellers);

        // When
        List<User> result = userService.getAllStorytellers();

        // Then
        assertEquals(1, result.size());
        assertEquals(testStoryteller, result.get(0));
        verify(userRepository).findAllStorytellers();
    }

    @Test
    void getActiveUsers_ReturnsActiveUsers() {
        // Given
        List<User> activeUsers = Arrays.asList(testUser, testStoryteller);
        when(userRepository.findByEnabledTrue()).thenReturn(activeUsers);

        // When
        List<User> result = userService.getActiveUsers();

        // Then
        assertEquals(2, result.size());
        verify(userRepository).findByEnabledTrue();
    }

    @Test
    void isUsernameAvailable_AvailableUsername_ReturnsTrue() {
        // Given
        when(userRepository.existsByUsername("available")).thenReturn(false);

        // When
        boolean result = userService.isUsernameAvailable("available");

        // Then
        assertTrue(result);
        verify(userRepository).existsByUsername("available");
    }

    @Test
    void isUsernameAvailable_TakenUsername_ReturnsFalse() {
        // Given
        when(userRepository.existsByUsername("taken")).thenReturn(true);

        // When
        boolean result = userService.isUsernameAvailable("taken");

        // Then
        assertFalse(result);
        verify(userRepository).existsByUsername("taken");
    }

    @Test
    void isEmailAvailable_AvailableEmail_ReturnsTrue() {
        // Given
        when(userRepository.existsByEmail("<EMAIL>")).thenReturn(false);

        // When
        boolean result = userService.isEmailAvailable("<EMAIL>");

        // Then
        assertTrue(result);
        verify(userRepository).existsByEmail("<EMAIL>");
    }

    @Test
    void deleteUser_ExistingUser_DeletesUser() {
        // Given
        when(userRepository.existsById("user-id-123")).thenReturn(true);

        // When
        userService.deleteUser("user-id-123");

        // Then
        verify(userRepository).existsById("user-id-123");
        verify(userRepository).deleteById("user-id-123");
    }

    @Test
    void deleteUser_NonExistentUser_ThrowsException() {
        // Given
        when(userRepository.existsById("nonexistent")).thenReturn(false);

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
                () -> userService.deleteUser("nonexistent"));

        assertEquals("User not found: nonexistent", exception.getMessage());
        verify(userRepository, never()).deleteById(anyString());
    }
}
