package com.botc.assistant.roles.outsiders;

/**
 * <PERSON> - Outsider role
 * 
 * Each night, choose a player (not yourself): tomorrow, you may only vote if they are voting too.
 * 
 * The <PERSON>'s vote is restricted based on their chosen master.
 */
public class Butler extends Outsider {
    
    public static final String ROLE_ID = "butler";
    public static final String ROLE_NAME = "Butler";
    public static final String ROLE_DESCRIPTION = "Each night, choose a player (not yourself): tomorrow, you may only vote if they are voting too.";
    
    private Butler(Builder builder) {
        super(builder);
    }
    
    public static Builder builder() {
        return new Builder();
    }
    
    public static Butler create() {
        return builder().build();
    }
    
    /**
     * Builder for <PERSON> role
     */
    public static class Builder extends Outsider.Builder<Builder> {
        
        public Builder() {
            id(ROLE_ID);
            name(ROLE_NAME);
            description(ROLE_DESCRIPTION);
            hasNightAbility(true);
            nightPriority(25); // Each night choice
            setupInfo("Chooses a master each night, can only vote when master votes");
        }
        
        @Override
        protected Builder self() {
            return this;
        }
        
        @Override
        public Butler build() {
            return new Butler(this);
        }
    }
}
