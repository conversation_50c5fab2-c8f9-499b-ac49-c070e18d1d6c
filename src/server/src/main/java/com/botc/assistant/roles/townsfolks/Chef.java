package com.botc.assistant.roles.townsfolks;

/**
 * Chef - Townsfolk role
 *
 * You start knowing how many pairs of evil players there are.
 *
 * The Chef learns information about evil players on the first night.
 * They learn how many pairs of evil players there are, which helps
 * determine the total number of evil players in the game.
 */
public class Chef extends TownsFolk {

    public static final String ROLE_ID = "chef";
    public static final String ROLE_NAME = "Chef";
    public static final String ROLE_DESCRIPTION = "You start knowing how many pairs of evil players there are.";

    private Chef(Builder builder) {
        super(builder);
    }

    public static Builder builder() {
        return new Builder();
    }

    public static Chef create() {
        return builder().build();
    }

    /**
     * Builder for Chef role
     */
    public static class Builder extends TownsFolk.Builder<Builder> {

        public Builder() {
            id(ROLE_ID);
            name(ROLE_NAME);
            description(ROLE_DESCRIPTION);
            hasNightAbility(true);
            nightPriority(1); // First night information
            setupInfo("Learns how many pairs of evil players exist");
        }

        @Override
        protected Builder self() {
            return this;
        }

        @Override
        public Chef build() {
            return new Chef(this);
        }
    }
}
