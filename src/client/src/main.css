main{
    display: flex;
    justify-content: center;
}

footer{
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #2B0F38;
    color: #FFFB9D;
    height: 80px;
}

section{
    padding-top: 20px;
    padding-bottom: 20px;
    padding-left: 10%;
    padding-right: 10%;
}


/* Nav bar home */
header{
    font-family: 'cc-goth-chic', sans-serif;
    padding-right: 2%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    background-color: #2B0F38;
}

#menu_options{
    min-width: 100px;
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 20px;
}

#menu_links{
    display: flex;
    justify-content: space-between;
    gap: 20px;
}

#menu_links a{
    font-size: 1.5em;
    text-decoration: none;
    color: #FFFB9D;
}


/* Landing page */

#landing{
    display: flex;
    flex-direction: column;
}

section h1{
    font-family: 'cc-goth-chic', sans-serif;
    font-weight: 400;
    font-style: normal;
    text-align: center;
    padding-bottom: 20px;
}

section p{
    font-family: sans-serif;
    font-weight: 400;
    font-style: normal;
}

#mj_side_promo h1{
    color: #795C4E;
}

.promo{
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    gap: 5%;
}

#mj_side_promo .promo p{
    color: #4F3A2F;
}

.promo p{
    font-size: 24px;
    text-wrap: wrap;
    width: 50%;
}

.promo img{
    width: 50%;
}

#player_side_promo{
    background-color: #64618D;
}

#player_side_promo h1, p{
    color: white;
}