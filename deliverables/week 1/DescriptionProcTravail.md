# Description du processus de travail

Berberat Alex  
Gorgerat <PERSON>

## Outils
Nous avons décidé d'utiliser Github comme outil de développement de notre application. 
Afin de structurer correctement nos itérations, nous avons mis en place un Kanban composé de 5 colonnes: 
|Colonne|Description|
|:-----:|:---------:|
|`Backlog`|Fourre-Issue: permet de regrouper toutes les issues avant le processus de sélection pour la prochaine itération|
|`Select for iteration`|Cette colonne regroupe au début de l'itération toutes les issues qui devront être traitées|
|`In progress`|Issues ouvertes et en cours de traitement|
|`In review`|Issues en attente (Pull request)|
|`Done`|Issues terminées|

Ainsi, chaque tâche fera l'objet d'une issue et sera sélectionnée lors de l'itération en temps voulu. Le suivi des tâches est ainsi simplifié et permet de connaître en tout temps l'avancée de chaque tâche et du projet en général.
