import { describe, it, expect } from 'vitest'
import { render, screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import App from './App'

describe('App Component', () => {
  it('renders the main title', () => {
    render(<App />)
    expect(screen.getByText('Blood on the Clocktower')).toBeInTheDocument()
    expect(screen.getByText('Assistant')).toBeInTheDocument()
  })

  it('renders the hero description', () => {
    render(<App />)
    expect(screen.getByText(/Votre compagnon ultime pour le jeu de déduction sociale/)).toBeInTheDocument()
  })

  it('renders action buttons', () => {
    render(<App />)
    expect(screen.getByRole('button', { name: 'Commencer' })).toBeInTheDocument()
    expect(screen.getByRole('button', { name: 'En savoir plus' })).toBeInTheDocument()
  })

  it('renders about section with cards', () => {
    render(<App />)
    expect(screen.getByText("Qu'est-ce que Blood on the Clocktower ?")).toBeInTheDocument()
    expect(screen.getByText('Déduction Sociale')).toBeInTheDocument()
    expect(screen.getByText('Phases Jour & Nuit')).toBeInTheDocument()
    expect(screen.getByText('Rôles Uniques')).toBeInTheDocument()
  })

  it('renders features section', () => {
    render(<App />)
    expect(screen.getByText("Fonctionnalités de l'Assistant")).toBeInTheDocument()
    expect(screen.getByText('Référence des Rôles')).toBeInTheDocument()
    expect(screen.getByText('Minuteur de Jeu')).toBeInTheDocument()
    expect(screen.getByText('Prise de Notes')).toBeInTheDocument()
    expect(screen.getByText('Créateur de Scripts')).toBeInTheDocument()
  })

  it('renders footer with copyright information', () => {
    render(<App />)
    expect(screen.getByText(/Blood on the Clocktower Assistant • Créé avec ❤️/)).toBeInTheDocument()
    expect(screen.getByText(/Blood on the Clocktower est une marque déposée/)).toBeInTheDocument()
  })

  it('has proper semantic structure', () => {
    render(<App />)
    expect(screen.getByRole('banner')).toBeInTheDocument() // header
    expect(screen.getByRole('contentinfo')).toBeInTheDocument() // footer
    expect(screen.getAllByRole('heading', { level: 1 })).toHaveLength(1)
    expect(screen.getAllByRole('heading', { level: 2 })).toHaveLength(2)
    expect(screen.getAllByRole('heading', { level: 3 })).toHaveLength(7)
  })

  it('buttons are interactive', async () => {
    const user = userEvent.setup()
    render(<App />)
    
    const startButton = screen.getByRole('button', { name: 'Commencer' })
    const learnMoreButton = screen.getByRole('button', { name: 'En savoir plus' })
    
    // Buttons should be clickable (even if they don't do anything yet)
    await user.click(startButton)
    await user.click(learnMoreButton)
    
    expect(startButton).toBeInTheDocument()
    expect(learnMoreButton).toBeInTheDocument()
  })

  it('has clock visual elements', () => {
    render(<App />)
    const clockElements = document.querySelectorAll('.clock-face, .clock-hand, .clock-center')
    expect(clockElements.length).toBeGreaterThan(0)
  })
})
