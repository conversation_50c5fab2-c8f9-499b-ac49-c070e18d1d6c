package com.botc.assistant.roles.townsfolks;

/**
 * Mayor - Townsfolk role
 * 
 * If only 3 players live & no execution occurs, your team wins. If you die at night, another player might die instead.
 * 
 * The Mayor has a special win condition and protection ability.
 */
public class Mayor extends TownsFolk {
    
    public static final String ROLE_ID = "mayor";
    public static final String ROLE_NAME = "Mayor";
    public static final String ROLE_DESCRIPTION = "If only 3 players live & no execution occurs, your team wins. If you die at night, another player might die instead.";
    
    private Mayor(Builder builder) {
        super(builder);
    }
    
    public static Builder builder() {
        return new Builder();
    }
    
    public static Mayor create() {
        return builder().build();
    }
    
    /**
     * Builder for Mayor role
     */
    public static class Builder extends TownsFolk.Builder<Builder> {
        
        public Builder() {
            id(ROLE_ID);
            name(ROLE_NAME);
            description(ROLE_DESCRIPTION);
            setupInfo("Special win condition: good wins if only 3 players live and no execution occurs");
        }
        
        @Override
        protected Builder self() {
            return this;
        }
        
        @Override
        public Mayor build() {
            return new Mayor(this);
        }
    }
}
