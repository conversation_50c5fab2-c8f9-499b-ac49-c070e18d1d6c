package com.botc.assistant.roles.townsfolks;

/**
 * Slayer - Townsfolk role
 * 
 * Once per game, during the day, publicly choose a player: if they are the Demon, they die.
 * 
 * The Slayer can kill the Demon during the day but only once per game.
 */
public class Slayer extends TownsFolk {
    
    public static final String ROLE_ID = "slayer";
    public static final String ROLE_NAME = "Slayer";
    public static final String ROLE_DESCRIPTION = "Once per game, during the day, publicly choose a player: if they are the Demon, they die.";
    
    private Slayer(Builder builder) {
        super(builder);
    }
    
    public static Builder builder() {
        return new Builder();
    }
    
    public static Slayer create() {
        return builder().build();
    }
    
    /**
     * Builder for Slayer role
     */
    public static class Builder extends TownsFolk.Builder<Builder> {
        
        public Builder() {
            id(ROLE_ID);
            name(ROLE_NAME);
            description(ROLE_DESCRIPTION);
            hasDayAbility(true);
            setupInfo("Can kill the Demon during the day, once per game");
        }
        
        @Override
        protected Builder self() {
            return this;
        }
        
        @Override
        public Slayer build() {
            return new Slayer(this);
        }
    }
}
