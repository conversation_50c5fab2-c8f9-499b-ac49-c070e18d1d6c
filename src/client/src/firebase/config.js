// Firebase configuration for Blood on the Clock Tower Assistant
import { initializeApp } from 'firebase/app';
import { getAuth, connectAuthEmulator } from 'firebase/auth';
import { getFirestore, connectFirestoreEmulator } from 'firebase/firestore';

// Firebase configuration object
// In production, these would come from environment variables
const firebaseConfig = {
  apiKey: "AIzaSyAtCGjQmnSY1HQu3f51NMecHrSbcFIpXj4",
  authDomain: "botc-assistant.firebaseapp.com",
  projectId: "botc-assistant",
  storageBucket: "botc-assistant.firebasestorage.app",
  messagingSenderId: "174594655975",
  appId: "1:174594655975:web:edb8b1314db29cca45c442",
  measurementId: "G-Y7V28L1TQ8"
};


// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase Authentication and get a reference to the service
export const auth = getAuth(app);

// Initialize Cloud Firestore and get a reference to the service
export const db = getFirestore(app);

// For development, you can connect to the Firebase emulators
// Uncomment these lines if you're using Firebase emulators locally
/*
if (import.meta.env.DEV) {
  // Connect to Auth emulator
  if (!auth._delegate._config.emulator) {
    connectAuthEmulator(auth, 'http://localhost:9099');
  }
  
  // Connect to Firestore emulator
  if (!db._delegate._settings?.host?.includes('localhost')) {
    connectFirestoreEmulator(db, 'localhost', 8080);
  }
}
*/

export default app;
