package com.botc.assistant.repository;

import com.botc.assistant.model.Game;
import com.google.cloud.firestore.DocumentSnapshot;
import com.google.cloud.firestore.Firestore;
import com.google.cloud.firestore.QueryDocumentSnapshot;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ExecutionException;

/**
 * Repository for managing Game entities in Firestore.
 */
@Repository
public class GameRepository {

    private final Firestore firestore;
    private static final String COLLECTION_NAME = "games";

    @Autowired
    public GameRepository(@Autowired(required = false) Firestore firestore) {
        this.firestore = firestore;
    }

    /**
     * Save a game to Firestore.
     */
    public Game save(Game game) {
        if (firestore == null) {
            throw new RuntimeException("Firestore is not configured");
        }

        try {
            Map<String, Object> gameData = gameToMap(game);
            firestore.collection(COLLECTION_NAME).document(game.getId()).set(gameData);
            return game;
        } catch (Exception e) {
            throw new RuntimeException("Failed to save game: " + e.getMessage(), e);
        }
    }

    /**
     * Find a game by ID.
     */
    public Optional<Game> findById(String id) {
        if (firestore == null) {
            return Optional.empty();
        }

        try {
            var document = firestore.collection(COLLECTION_NAME).document(id).get().get();
            if (document.exists()) {
                return Optional.of(documentToGame(document));
            }
            return Optional.empty();
        } catch (InterruptedException | ExecutionException e) {
            System.err.println("Error finding game by ID: " + e.getMessage());
            return Optional.empty();
        }
    }

    /**
     * Find a game by its join code.
     */
    public Optional<Game> findByCode(String code) {
        if (firestore == null) {
            return Optional.empty();
        }

        try {
            var query = firestore.collection(COLLECTION_NAME)
                    .whereEqualTo("code", code)
                    .whereEqualTo("isActive", true)
                    .get().get();
            
            if (!query.isEmpty()) {
                var document = query.getDocuments().get(0);
                return Optional.of(documentToGame(document));
            }
            return Optional.empty();
        } catch (InterruptedException | ExecutionException e) {
            System.err.println("Error finding game by code: " + e.getMessage());
            return Optional.empty();
        }
    }

    /**
     * Find all active games.
     */
    public List<Game> findAllActive() {
        if (firestore == null) {
            return new ArrayList<>();
        }

        try {
            List<Game> games = new ArrayList<>();
            var documents = firestore.collection(COLLECTION_NAME)
                    .whereEqualTo("isActive", true)
                    .get().get();
            
            for (var document : documents.getDocuments()) {
                games.add(documentToGame(document));
            }
            
            return games;
        } catch (InterruptedException | ExecutionException e) {
            System.err.println("Error retrieving active games: " + e.getMessage());
            return new ArrayList<>();
        }
    }

    /**
     * Find games by storyteller ID.
     */
    public List<Game> findByStorytellerId(String storytellerId) {
        if (firestore == null) {
            return new ArrayList<>();
        }

        try {
            List<Game> games = new ArrayList<>();
            var documents = firestore.collection(COLLECTION_NAME)
                    .whereEqualTo("storytellerId", storytellerId)
                    .get().get();
            
            for (var document : documents.getDocuments()) {
                games.add(documentToGame(document));
            }
            
            return games;
        } catch (InterruptedException | ExecutionException e) {
            System.err.println("Error retrieving games by storyteller: " + e.getMessage());
            return new ArrayList<>();
        }
    }

    /**
     * Delete a game by ID.
     */
    public void deleteById(String id) {
        if (firestore == null) {
            return;
        }

        try {
            firestore.collection(COLLECTION_NAME).document(id).delete();
        } catch (Exception e) {
            System.err.println("Error deleting game: " + e.getMessage());
        }
    }

    /**
     * Update specific fields of a game.
     */
    public void updateFields(String id, Map<String, Object> updates) {
        if (firestore == null) {
            return;
        }

        try {
            updates.put("lastUpdated", LocalDateTime.now().toString());
            firestore.collection(COLLECTION_NAME).document(id).update(updates);
        } catch (Exception e) {
            System.err.println("Error updating game fields: " + e.getMessage());
        }
    }

    /**
     * Convert Game object to Firestore document map.
     */
    private Map<String, Object> gameToMap(Game game) {
        Map<String, Object> data = new HashMap<>();
        data.put("id", game.getId());
        data.put("name", game.getName());
        data.put("code", game.getCode());
        data.put("maxPlayers", game.getMaxPlayers());
        data.put("currentPlayers", game.getCurrentPlayers());
        data.put("createdAt", game.getCreatedAt().toString());
        data.put("lastUpdated", game.getLastUpdated().toString());
        data.put("state", game.getState().name());
        data.put("currentPhase", game.getCurrentPhase().name());
        data.put("dayNumber", game.getDayNumber());
        data.put("storytellerId", game.getStorytellerId());
        data.put("isActive", game.isActive());
        data.put("storytellerNotes", game.getStorytellerNotes());
        
        return data;
    }

    /**
     * Convert Firestore document to Game object.
     */
    private Game documentToGame(DocumentSnapshot document) {
        Game game = new Game();
        
        game.setId(document.getString("id"));
        game.setName(document.getString("name"));
        game.setCode(document.getString("code"));
        
        Long maxPlayers = document.getLong("maxPlayers");
        if (maxPlayers != null) {
            game.setMaxPlayers(maxPlayers.intValue());
        }
        
        Long currentPlayers = document.getLong("currentPlayers");
        if (currentPlayers != null) {
            game.setCurrentPlayers(currentPlayers.intValue());
        }
        
        String createdAtStr = document.getString("createdAt");
        if (createdAtStr != null) {
            game.setCreatedAt(LocalDateTime.parse(createdAtStr));
        }
        
        String lastUpdatedStr = document.getString("lastUpdated");
        if (lastUpdatedStr != null) {
            game.setLastUpdated(LocalDateTime.parse(lastUpdatedStr));
        }
        
        String stateStr = document.getString("state");
        if (stateStr != null) {
            game.setState(Game.GameState.valueOf(stateStr));
        }
        
        String phaseStr = document.getString("currentPhase");
        if (phaseStr != null) {
            game.setCurrentPhase(Game.GamePhase.valueOf(phaseStr));
        }
        
        Long dayNumber = document.getLong("dayNumber");
        if (dayNumber != null) {
            game.setDayNumber(dayNumber.intValue());
        }
        
        game.setStorytellerId(document.getString("storytellerId"));
        
        Boolean isActive = document.getBoolean("isActive");
        if (isActive != null) {
            game.setActive(isActive);
        }
        
        game.setStorytellerNotes(document.getString("storytellerNotes"));
        
        return game;
    }
}
