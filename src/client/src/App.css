/* Blood on the Clocktower Assistant Styles */

.app {
    min-height: 100vh;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    color: #ffffff;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
}

/* Hero Section */
.hero-section {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 20%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(255, 119, 198, 0.2) 0%, transparent 50%);
    pointer-events: none;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    max-width: 1200px;
    width: 100%;
    align-items: center;
    z-index: 1;
    position: relative;
}

.hero-text {
    text-align: left;
}

.hero-title {
    font-size: 4rem;
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, #ffffff 0%, #a78bfa 50%, #ec4899 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    display: block;
    font-size: 2.5rem;
    font-weight: 600;
    margin-top: 0.5rem;
    color: #fbbf24;
}

.hero-description {
    font-size: 1.25rem;
    line-height: 1.6;
    margin-bottom: 2rem;
    color: #e5e7eb;
    max-width: 500px;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.btn-primary,
.btn-secondary {
    padding: 1rem 2rem;
    border: none;
    border-radius: 12px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.btn-primary {
    background: linear-gradient(135deg, #8b5cf6 0%, #ec4899 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(139, 92, 246, 0.4);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(139, 92, 246, 0.6);
}

.btn-secondary {
    background: transparent;
    color: #ffffff;
    border: 2px solid #8b5cf6;
}

.btn-secondary:hover {
    background: #8b5cf6;
    transform: translateY(-2px);
}

/* Hero Visual - Clocktower */
.hero-visual {
    display: flex;
    justify-content: center;
    align-items: center;
}

.clocktower-icon {
    width: 300px;
    height: 300px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.clock-face {
    width: 200px;
    height: 200px;
    border: 8px solid #8b5cf6;
    border-radius: 50%;
    position: relative;
    background: linear-gradient(135deg, #1e1b4b 0%, #312e81 100%);
    box-shadow: 0 0 30px rgba(139, 92, 246, 0.5),
        inset 0 0 20px rgba(139, 92, 246, 0.2);
    animation: clockGlow 3s ease-in-out infinite alternate;
}

.clock-hand {
    position: absolute;
    background: #fbbf24;
    border-radius: 2px;
    transform-origin: bottom center;
    left: 50%;
    bottom: 50%;
}

.hour-hand {
    width: 4px;
    height: 60px;
    margin-left: -2px;
    transform: translateX(-50%) rotate(45deg);
    animation: hourRotate 12s linear infinite;
}

.minute-hand {
    width: 2px;
    height: 80px;
    margin-left: -1px;
    transform: translateX(-50%) rotate(90deg);
    animation: minuteRotate 1s linear infinite;
}

.clock-center {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 12px;
    height: 12px;
    background: #fbbf24;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    z-index: 2;
}

@keyframes clockGlow {
    0% {
        box-shadow: 0 0 30px rgba(139, 92, 246, 0.5), inset 0 0 20px rgba(139, 92, 246, 0.2);
    }

    100% {
        box-shadow: 0 0 50px rgba(139, 92, 246, 0.8), inset 0 0 30px rgba(139, 92, 246, 0.4);
    }
}

@keyframes hourRotate {
    0% {
        transform: translateX(-50%) rotate(45deg);
    }

    100% {
        transform: translateX(-50%) rotate(405deg);
    }
}

@keyframes minuteRotate {
    0% {
        transform: translateX(-50%) rotate(90deg);
    }

    100% {
        transform: translateX(-50%) rotate(450deg);
    }
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

/* Section Styles */
.about-section,
.features-section {
    padding: 6rem 0;
}

.about-section {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
}

.section-title {
    font-size: 3rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: 3rem;
    background: linear-gradient(135deg, #ffffff 0%, #a78bfa 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* About Grid */
.about-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.about-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    padding: 2rem;
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.about-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(139, 92, 246, 0.3);
}

.card-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.about-card h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #fbbf24;
}

.about-card p {
    color: #e5e7eb;
    line-height: 1.6;
}

/* Features Grid */
.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-top: 3rem;
}

.feature-card {
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.1) 0%, rgba(236, 72, 153, 0.1) 100%);
    border: 1px solid rgba(139, 92, 246, 0.3);
    border-radius: 12px;
    padding: 1.5rem;
    transition: transform 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-3px);
}

.feature-card h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #a78bfa;
}

.feature-card p {
    color: #d1d5db;
    line-height: 1.5;
    margin: 0;
}

/* Footer */
.app-footer {
    background: rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    padding: 2rem 0;
    text-align: center;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.app-footer p {
    margin: 0.5rem 0;
    color: #d1d5db;
}

.footer-note {
    font-size: 0.9rem;
    color: #9ca3af;
    margin-top: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.8rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .about-section,
    .features-section {
        padding: 4rem 0;
    }

    .container {
        padding: 0 1rem;
    }

    .clocktower-icon {
        width: 200px;
        height: 200px;
    }

    .clock-face {
        width: 150px;
        height: 150px;
    }
}

/* Testing Interface Styles */
.testing-interface {
    background: #f8f9fa;
    min-height: 100vh;
}

.testing-header {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.testing-header h1 {
    font-size: 1.5rem;
    font-weight: 600;
}

.status-bar {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.user-status,
.game-status,
.loading-status {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
}

.loading-status {
    background: rgba(255, 193, 7, 0.3);
}

/* Alert Messages */
.alert {
    padding: 1rem 2rem;
    margin: 0;
    font-weight: 500;
    text-align: center;
}

.alert-error {
    background: #f8d7da;
    color: #721c24;
    border-bottom: 2px solid #f5c6cb;
}

.alert-success {
    background: #d4edda;
    color: #155724;
    border-bottom: 2px solid #c3e6cb;
}

/* Tab Navigation */
.tab-navigation {
    background: white;
    display: flex;
    border-bottom: 2px solid #e9ecef;
    overflow-x: auto;
}

.tab-button {
    background: none;
    border: none;
    padding: 1rem 1.5rem;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    color: #6c757d;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.tab-button:hover {
    background: #f8f9fa;
    color: #495057;
}

.tab-button.active {
    color: #007bff;
    border-bottom-color: #007bff;
    background: #f8f9fa;
}

/* Main Content */
.testing-content {
    flex: 1;
    padding: 2rem;
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
}

.tab-content {
    background: white;
    border-radius: 8px;
    padding: 2rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.tab-content h2 {
    margin-bottom: 2rem;
    color: #2c3e50;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 0.5rem;
}

/* Form Sections */
.form-section {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #007bff;
}

.form-section h3 {
    margin-bottom: 1rem;
    color: #495057;
    font-size: 1.1rem;
}

.test-form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    max-width: 400px;
}

.test-form input,
.test-form select {
    padding: 0.75rem;
    border: 2px solid #e9ecef;
    border-radius: 4px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.test-form input:focus,
.test-form select:focus {
    outline: none;
    border-color: #007bff;
}

.test-form button {
    padding: 0.75rem 1.5rem;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.test-form button:hover:not(:disabled) {
    background: #0056b3;
}

.test-form button:disabled {
    background: #6c757d;
    cursor: not-allowed;
}

/* Checkbox Styling */
.checkbox-label {
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 10px 0;
    font-size: 14px;
    color: #333;
}

.checkbox-label input[type="checkbox"] {
    width: auto;
    margin: 0;
}

/* User List Styling */
.users-list {
    display: grid;
    gap: 15px;
    margin-top: 15px;
}

.user-card {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
}

.user-card.clickable {
    cursor: pointer;
}

.user-card.clickable:hover {
    background: #e9ecef;
    border-color: #007bff;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.user-card.current-user {
    background: #d4edda;
    border-color: #28a745;
    box-shadow: 0 0 0 2px rgba(40, 167, 69, 0.25);
}

.user-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 8px;
}

.role-badge {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.8em;
    font-weight: bold;
    text-transform: uppercase;
}

.role-badge.player {
    background: #e3f2fd;
    color: #1976d2;
}

.role-badge.storyteller {
    background: #fce4ec;
    color: #c2185b;
}

.current-badge {
    background: #28a745;
    color: white;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 0.7em;
    font-weight: bold;
}

.user-email {
    color: #6c757d;
    font-size: 0.9em;
    margin: 5px 0;
}

.user-stats {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin: 8px 0;
    font-size: 0.9em;
}

.user-stats span {
    color: #495057;
}

.user-id {
    color: #6c757d;
    font-size: 0.8em;
    font-family: monospace;
    margin: 5px 0 0 0;
}

.users-hint {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 6px;
    padding: 10px;
    margin: 10px 0;
    color: #856404;
    font-size: 0.9em;
}

.logout-btn {
    background: #dc3545;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    margin-top: 10px;
    transition: background-color 0.2s;
}

.logout-btn:hover {
    background: #c82333;
}

.user-card p {
    margin: 5px 0;
    font-size: 14px;
}

.user-card strong {
    color: #007bff;
}

.game-selection {
    margin-bottom: 15px;
}

.game-selection label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    color: #495057;
}

.game-selection select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
    background-color: white;
}

.game-selection select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.hint {
    font-size: 0.9em;
    color: #6c757d;
    margin: 5px 0;
    font-style: italic;
}

/* Button Groups */
.button-group {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.button-group button {
    padding: 0.5rem 1rem;
    background: #28a745;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background-color 0.3s ease;
}

.button-group button:hover:not(:disabled) {
    background: #218838;
}

.button-group button:disabled {
    background: #6c757d;
    cursor: not-allowed;
}

/* Info Display Sections */
.user-info,
.game-info {
    background: white;
    padding: 1rem;
    border-radius: 4px;
    border: 1px solid #e9ecef;
}

.user-info p,
.game-info p {
    margin: 0.5rem 0;
    color: #495057;
}

/* Lists */
.games-list,
.players-list,
.player-roles {
    display: grid;
    gap: 1rem;
    margin-top: 1rem;
}

.game-item,
.player-item,
.player-role-item {
    background: white;
    padding: 1rem;
    border-radius: 4px;
    border: 1px solid #e9ecef;
    transition: box-shadow 0.3s ease;
}

.game-item:hover,
.player-item:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.game-item h4 {
    margin: 0 0 0.5rem 0;
    color: #2c3e50;
}

.game-item p,
.player-item p {
    margin: 0.25rem 0;
    color: #6c757d;
    font-size: 0.9rem;
}

.game-item button {
    margin-top: 0.5rem;
    padding: 0.25rem 0.75rem;
    background: #17a2b8;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8rem;
}

.game-item button:hover {
    background: #138496;
}

/* Roles Grid */
.roles-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.player-roles {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1.5rem;
}

.player-role-item h4 {
    margin: 0 0 1rem 0;
    color: #2c3e50;
    text-align: center;
}

/* Voting Styles */
.voting-controls {
    margin-bottom: 1rem;
}

.players-voting-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.nominate-btn {
    padding: 0.5rem 1rem;
    background: #ffc107;
    color: #212529;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: background-color 0.3s ease;
}

.nominate-btn:hover:not(:disabled) {
    background: #e0a800;
}

.nominate-btn:disabled {
    background: #6c757d;
    color: white;
    cursor: not-allowed;
}

.vote-buttons {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.vote-btn {
    padding: 1rem 2rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1.1rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.vote-btn.yes {
    background: #28a745;
    color: white;
}

.vote-btn.yes:hover:not(:disabled) {
    background: #218838;
    transform: translateY(-2px);
}

.vote-btn.no {
    background: #dc3545;
    color: white;
}

.vote-btn.no:hover:not(:disabled) {
    background: #c82333;
    transform: translateY(-2px);
}

.vote-btn:disabled {
    background: #6c757d;
    cursor: not-allowed;
    transform: none;
}

/* Timer Styles */
.timer-controls-section {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.timer-settings {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.timer-settings label {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    color: #495057;
    font-weight: 500;
}

.timer-settings input,
.timer-settings select {
    padding: 0.5rem;
    border: 2px solid #e9ecef;
    border-radius: 4px;
    font-size: 1rem;
}

.timer-actions {
    display: flex;
    gap: 1rem;
}

.timer-actions button {
    padding: 0.75rem 1.5rem;
    background: #6f42c1;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.3s ease;
}

.timer-actions button:hover {
    background: #5a32a3;
}

.timer-display-section {
    display: flex;
    justify-content: center;
    margin: 2rem 0;
}

/* Instructions */
.instructions {
    background: #e7f3ff;
    border: 1px solid #b3d9ff;
    border-radius: 4px;
    padding: 1rem;
    margin-top: 1rem;
}

.instructions ol,
.instructions ul {
    margin: 0.5rem 0;
    padding-left: 1.5rem;
}

.instructions li {
    margin: 0.25rem 0;
    color: #495057;
}

.instructions p {
    margin: 0.5rem 0;
    color: #6c757d;
    font-style: italic;
}

/* Responsive Design for Testing Interface */
@media (max-width: 768px) {
    .testing-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .testing-header h1 {
        font-size: 1.2rem;
    }

    .status-bar {
        justify-content: center;
    }

    .testing-content {
        padding: 1rem;
    }

    .tab-content {
        padding: 1rem;
    }

    .timer-settings {
        flex-direction: column;
    }

    .vote-buttons {
        flex-direction: column;
    }

    .roles-grid {
        grid-template-columns: 1fr;
    }

    .player-roles {
        grid-template-columns: 1fr;
    }
}

/* Players List Styling */
.players-list {
    margin-top: 1rem;
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.players-list p {
    margin: 0 0 0.5rem 0;
    font-size: 0.9rem;
    color: #b0b0b0;
}

.players-list ul {
    margin: 0;
    padding-left: 1rem;
    list-style: none;
}

.players-list li {
    margin: 0.25rem 0;
    font-size: 0.85rem;
    color: #e0e0e0;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.players-list li::before {
    content: '👤';
    margin-right: 0.25rem;
}
