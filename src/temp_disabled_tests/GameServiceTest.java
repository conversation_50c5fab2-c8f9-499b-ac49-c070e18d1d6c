package com.botc.assistant.service;

import com.botc.assistant.model.Game;
import com.botc.assistant.model.Player;
import com.botc.assistant.model.User;
import com.botc.assistant.repository.GameRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for GameService.
 */
@ExtendWith(MockitoExtension.class)
class GameServiceTest {

    @Mock
    private GameRepository gameRepository;

    @Mock
    private NotificationService notificationService;

    @Mock
    private RoleService roleService;

    @InjectMocks
    private GameService gameService;

    private User testStoryteller;
    private User testPlayer;
    private Game testGame;
    private Player testPlayerEntity;

    @BeforeEach
    void setUp() {
        testStoryteller = new User("storyteller", "<EMAIL>", "password");
        testStoryteller.setId("st-id-123");
        testStoryteller.setUserRole(User.UserRole.STORYTELLER);

        testPlayer = new User("player", "<EMAIL>", "password");
        testPlayer.setId("player-id-456");
        testPlayer.setUserRole(User.UserRole.PLAYER);

        testGame = new Game("Test Game", 10, testStoryteller);
        testGame.setId("game-id-789");
        testGame.setState(Game.GameState.WAITING);

        testPlayerEntity = new Player(testPlayer, "Player One", 0, testGame);
        testPlayerEntity.setId("player-entity-id");
    }

    @Test
    void createGame_ValidData_ReturnsGame() {
        // Given
        when(gameRepository.save(any(Game.class))).thenReturn(testGame);

        // When
        Game result = gameService.createGame("Test Game", 10, testStoryteller);

        // Then
        assertNotNull(result);
        assertEquals("Test Game", result.getName());
        assertEquals(10, result.getMaxPlayers());
        assertEquals(testStoryteller, result.getStoryteller());
        verify(gameRepository).save(any(Game.class));
        verify(notificationService).sendCustomMessage(eq(testGame.getId()), anyString(), eq("game.created"));
    }

    @Test
    void findById_ExistingGame_ReturnsGame() {
        // Given
        when(gameRepository.findById("game-id-789")).thenReturn(Optional.of(testGame));

        // When
        Optional<Game> result = gameService.findById("game-id-789");

        // Then
        assertTrue(result.isPresent());
        assertEquals(testGame, result.get());
        verify(gameRepository).findById("game-id-789");
    }

    @Test
    void findById_NonExistentGame_ReturnsEmpty() {
        // Given
        when(gameRepository.findById("nonexistent")).thenReturn(Optional.empty());

        // When
        Optional<Game> result = gameService.findById("nonexistent");

        // Then
        assertFalse(result.isPresent());
        verify(gameRepository).findById("nonexistent");
    }

    @Test
    void findByCode_ExistingGame_ReturnsGame() {
        // Given
        String gameCode = "ABC123";
        testGame.setCode(gameCode);
        when(gameRepository.findByCode(gameCode)).thenReturn(Optional.of(testGame));

        // When
        Optional<Game> result = gameService.findByCode(gameCode);

        // Then
        assertTrue(result.isPresent());
        assertEquals(testGame, result.get());
        verify(gameRepository).findByCode(gameCode);
    }

    @Test
    void addPlayer_ValidData_ReturnsPlayer() {
        // Given
        when(gameRepository.findById("game-id-789")).thenReturn(Optional.of(testGame));
        when(gameRepository.save(testGame)).thenReturn(testGame);

        // When
        Player result = gameService.addPlayer("game-id-789", testPlayer, "Player One");

        // Then
        assertNotNull(result);
        assertEquals("Player One", result.getName());
        assertEquals(testPlayer, result.getUser());
        assertEquals(testGame, result.getGame());
        verify(gameRepository).findById("game-id-789");
        verify(gameRepository).save(testGame);
        verify(notificationService).notifyPlayerJoined(testGame, result);
    }

    @Test
    void addPlayer_GameNotFound_ThrowsException() {
        // Given
        when(gameRepository.findById("nonexistent")).thenReturn(Optional.empty());

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
                () -> gameService.addPlayer("nonexistent", testPlayer, "Player One"));

        assertEquals("Game not found: nonexistent", exception.getMessage());
        verify(gameRepository).findById("nonexistent");
        verify(gameRepository, never()).save(any(Game.class));
    }

    @Test
    void addPlayer_GameFull_ThrowsException() {
        // Given
        testGame.setCurrentPlayers(testGame.getMaxPlayers()); // Make game full
        when(gameRepository.findById("game-id-789")).thenReturn(Optional.of(testGame));

        // When & Then
        IllegalStateException exception = assertThrows(IllegalStateException.class,
                () -> gameService.addPlayer("game-id-789", testPlayer, "Player One"));

        assertEquals("Game is full", exception.getMessage());
        verify(gameRepository).findById("game-id-789");
        verify(gameRepository, never()).save(any(Game.class));
    }

    @Test
    void addPlayer_GameInProgress_ThrowsException() {
        // Given
        testGame.setState(Game.GameState.IN_PROGRESS);
        when(gameRepository.findById("game-id-789")).thenReturn(Optional.of(testGame));

        // When & Then
        IllegalStateException exception = assertThrows(IllegalStateException.class,
                () -> gameService.addPlayer("game-id-789", testPlayer, "Player One"));

        assertEquals("Cannot join game in progress", exception.getMessage());
        verify(gameRepository).findById("game-id-789");
        verify(gameRepository, never()).save(any(Game.class));
    }

    @Test
    void addPlayer_StorytellerTriesToJoinOwnGame_ThrowsException() {
        // Given
        User storyteller = testGame.getStoryteller();
        when(gameRepository.findById("game-id-789")).thenReturn(Optional.of(testGame));

        // When & Then
        IllegalStateException exception = assertThrows(IllegalStateException.class,
                () -> gameService.addPlayer("game-id-789", storyteller, "Storyteller Player"));

        assertEquals("Storyteller cannot join their own game as a player", exception.getMessage());
        verify(gameRepository).findById("game-id-789");
        verify(gameRepository, never()).save(any(Game.class));
        verify(notificationService, never()).notifyPlayerJoined(any(), any());
    }

    @Test
    void removePlayer_ValidData_RemovesPlayer() {
        // Given
        testGame.getPlayers().add(testPlayerEntity);
        when(gameRepository.findById("game-id-789")).thenReturn(Optional.of(testGame));

        // When
        gameService.removePlayer("game-id-789", "player-entity-id");

        // Then
        verify(gameRepository).findById("game-id-789");
        verify(gameRepository).save(testGame);
        verify(notificationService).notifyPlayerLeft(testGame, testPlayerEntity);
    }

    @Test
    void removePlayer_GameNotFound_ThrowsException() {
        // Given
        when(gameRepository.findById("nonexistent")).thenReturn(Optional.empty());

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
                () -> gameService.removePlayer("nonexistent", "player-entity-id"));

        assertEquals("Game not found: nonexistent", exception.getMessage());
        verify(gameRepository).findById("nonexistent");
        verify(gameRepository, never()).save(any(Game.class));
    }

    @Test
    void removePlayer_PlayerNotFound_ThrowsException() {
        // Given
        when(gameRepository.findById("game-id-789")).thenReturn(Optional.of(testGame));

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
                () -> gameService.removePlayer("game-id-789", "nonexistent-player"));

        assertEquals("Player not found: nonexistent-player", exception.getMessage());
        verify(gameRepository).findById("game-id-789");
        verify(gameRepository, never()).save(any(Game.class));
    }

    @Test
    void startGame_ValidGame_StartsGame() {
        // Given
        testGame.setCurrentPlayers(5); // Minimum players
        testGame.setRolesAssigned(true); // Roles must be assigned
        when(gameRepository.findById("game-id-789")).thenReturn(Optional.of(testGame));

        // When
        gameService.startGame("game-id-789");

        // Then
        verify(gameRepository).findById("game-id-789");
        verify(gameRepository).save(testGame);
        verify(notificationService).notifyGameStarted(testGame);
        assertEquals(Game.GameState.IN_PROGRESS, testGame.getState());
    }

    @Test
    void startGame_GameNotFound_ThrowsException() {
        // Given
        when(gameRepository.findById("nonexistent")).thenReturn(Optional.empty());

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
                () -> gameService.startGame("nonexistent"));

        assertEquals("Game not found: nonexistent", exception.getMessage());
        verify(gameRepository).findById("nonexistent");
        verify(gameRepository, never()).save(any(Game.class));
    }

    @Test
    void startGame_CannotStart_ThrowsException() {
        // Given
        testGame.setCurrentPlayers(3); // Below minimum
        when(gameRepository.findById("game-id-789")).thenReturn(Optional.of(testGame));

        // When & Then
        IllegalStateException exception = assertThrows(IllegalStateException.class,
                () -> gameService.startGame("game-id-789"));

        assertEquals("Game cannot be started", exception.getMessage());
        verify(gameRepository).findById("game-id-789");
        verify(gameRepository, never()).save(any(Game.class));
    }

    @Test
    void endGame_ValidGame_EndsGame() {
        // Given
        testGame.setState(Game.GameState.IN_PROGRESS);
        when(gameRepository.findById("game-id-789")).thenReturn(Optional.of(testGame));

        // When
        gameService.endGame("game-id-789", Game.GameState.GOOD_WINS);

        // Then
        verify(gameRepository).findById("game-id-789");
        verify(gameRepository).save(testGame);
        verify(notificationService).notifyGameEnd(testGame, Game.GameState.GOOD_WINS);
        assertEquals(Game.GameState.GOOD_WINS, testGame.getState());
        assertFalse(testGame.isActive());
    }

    @Test
    void getActiveGames_ReturnsActiveGames() {
        // Given
        List<Game> activeGames = Arrays.asList(testGame);
        when(gameRepository.findByIsActiveTrue()).thenReturn(activeGames);

        // When
        List<Game> result = gameService.getActiveGames();

        // Then
        assertEquals(1, result.size());
        assertEquals(testGame, result.get(0));
        verify(gameRepository).findByIsActiveTrue();
    }

    @Test
    void getGamesWaitingForPlayers_ReturnsWaitingGames() {
        // Given
        List<Game> waitingGames = Arrays.asList(testGame);
        when(gameRepository.findGamesWaitingForPlayers()).thenReturn(waitingGames);

        // When
        List<Game> result = gameService.getGamesWaitingForPlayers();

        // Then
        assertEquals(1, result.size());
        assertEquals(testGame, result.get(0));
        verify(gameRepository).findGamesWaitingForPlayers();
    }

    @Test
    void getGamesByStoryteller_ReturnsStorytellersGames() {
        // Given
        List<Game> storytellerGames = Arrays.asList(testGame);
        when(gameRepository.findByStoryteller(testStoryteller)).thenReturn(storytellerGames);

        // When
        List<Game> result = gameService.getGamesByStoryteller(testStoryteller);

        // Then
        assertEquals(1, result.size());
        assertEquals(testGame, result.get(0));
        verify(gameRepository).findByStoryteller(testStoryteller);
    }

    @Test
    void deleteGame_ValidGame_DeletesGame() {
        // Given
        testGame.setState(Game.GameState.GOOD_WINS); // Completed game
        when(gameRepository.findById("game-id-789")).thenReturn(Optional.of(testGame));

        // When
        gameService.deleteGame("game-id-789");

        // Then
        verify(gameRepository).findById("game-id-789");
        verify(gameRepository).delete(testGame);
    }

    @Test
    void deleteGame_GameInProgress_ThrowsException() {
        // Given
        testGame.setState(Game.GameState.IN_PROGRESS);
        when(gameRepository.findById("game-id-789")).thenReturn(Optional.of(testGame));

        // When & Then
        IllegalStateException exception = assertThrows(IllegalStateException.class,
                () -> gameService.deleteGame("game-id-789"));

        assertEquals("Cannot delete game in progress", exception.getMessage());
        verify(gameRepository).findById("game-id-789");
        verify(gameRepository, never()).delete(any(Game.class));
    }

    @Test
    void isGameCodeAvailable_AvailableCode_ReturnsTrue() {
        // Given
        when(gameRepository.existsByCode("ABC123")).thenReturn(false);

        // When
        boolean result = gameService.isGameCodeAvailable("ABC123");

        // Then
        assertTrue(result);
        verify(gameRepository).existsByCode("ABC123");
    }

    @Test
    void isGameCodeAvailable_TakenCode_ReturnsFalse() {
        // Given
        when(gameRepository.existsByCode("TAKEN")).thenReturn(true);

        // When
        boolean result = gameService.isGameCodeAvailable("TAKEN");

        // Then
        assertFalse(result);
        verify(gameRepository).existsByCode("TAKEN");
    }

    @Test
    void getGameStats_ReturnsCorrectStats() {
        // Given
        when(gameRepository.count()).thenReturn(10L);
        when(gameRepository.countByIsActiveTrue()).thenReturn(3L);
        when(gameRepository.countByState(Game.GameState.WAITING)).thenReturn(2L);
        when(gameRepository.countByState(Game.GameState.IN_PROGRESS)).thenReturn(1L);
        when(gameRepository.countByState(Game.GameState.GOOD_WINS)).thenReturn(4L);
        when(gameRepository.countByState(Game.GameState.EVIL_WINS)).thenReturn(3L);

        // When
        GameService.GameStats result = gameService.getGameStats();

        // Then
        assertEquals(10L, result.getTotalGames());
        assertEquals(3L, result.getActiveGames());
        assertEquals(2L, result.getWaitingGames());
        assertEquals(1L, result.getInProgressGames());
        assertEquals(7L, result.getCompletedGames()); // 4 + 3

        verify(gameRepository).count();
        verify(gameRepository).countByIsActiveTrue();
        verify(gameRepository, times(4)).countByState(any(Game.GameState.class));
    }
}
