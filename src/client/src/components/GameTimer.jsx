import { useState, useEffect } from 'react'
import './GameTimer.css'

const GameTimer = ({ 
  initialTime = 300, // 5 minutes default
  isRunning = false, 
  onTimeUp = () => {},
  onTick = () => {},
  phase = 'day'
}) => {
  const [timeLeft, setTimeLeft] = useState(initialTime)
  const [isActive, setIsActive] = useState(isRunning)

  useEffect(() => {
    setTimeLeft(initialTime)
  }, [initialTime])

  useEffect(() => {
    setIsActive(isRunning)
  }, [isRunning])

  useEffect(() => {
    let interval = null

    if (isActive && timeLeft > 0) {
      interval = setInterval(() => {
        setTimeLeft(time => {
          const newTime = time - 1
          onTick(newTime)
          
          if (newTime === 0) {
            onTimeUp()
            setIsActive(false)
          }
          
          return newTime
        })
      }, 1000)
    } else if (!isActive || timeLeft === 0) {
      clearInterval(interval)
    }

    return () => clearInterval(interval)
  }, [isActive, timeLeft, onTick, onTimeUp])

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  const getProgressPercentage = () => {
    return ((initialTime - timeLeft) / initialTime) * 100
  }

  const handleStart = () => setIsActive(true)
  const handlePause = () => setIsActive(false)
  const handleReset = () => {
    setTimeLeft(initialTime)
    setIsActive(false)
  }

  return (
    <div className={`game-timer ${phase}`} data-testid="game-timer">
      <div className="timer-header">
        <h3 className="timer-phase">{phase === 'day' ? 'Jour' : 'Nuit'}</h3>
        <div className="timer-controls">
          <button 
            onClick={handleStart} 
            disabled={isActive || timeLeft === 0}
            className="timer-btn start-btn"
            aria-label="Démarrer le minuteur"
          >
            ▶️
          </button>
          <button 
            onClick={handlePause} 
            disabled={!isActive}
            className="timer-btn pause-btn"
            aria-label="Mettre en pause le minuteur"
          >
            ⏸️
          </button>
          <button 
            onClick={handleReset}
            className="timer-btn reset-btn"
            aria-label="Remettre à zéro le minuteur"
          >
            🔄
          </button>
        </div>
      </div>
      
      <div className="timer-display">
        <div className="time-text" data-testid="time-display">
          {formatTime(timeLeft)}
        </div>
        <div className="progress-ring">
          <svg className="progress-svg" width="120" height="120">
            <circle
              className="progress-background"
              cx="60"
              cy="60"
              r="50"
              strokeWidth="8"
              fill="none"
            />
            <circle
              className="progress-foreground"
              cx="60"
              cy="60"
              r="50"
              strokeWidth="8"
              fill="none"
              strokeDasharray={`${2 * Math.PI * 50}`}
              strokeDashoffset={`${2 * Math.PI * 50 * (1 - getProgressPercentage() / 100)}`}
              transform="rotate(-90 60 60)"
            />
          </svg>
        </div>
      </div>
      
      <div className="timer-status">
        <span className={`status-indicator ${isActive ? 'running' : timeLeft === 0 ? 'finished' : 'paused'}`}>
          {isActive ? 'En cours' : timeLeft === 0 ? 'Terminé' : 'En pause'}
        </span>
      </div>
    </div>
  )
}

export default GameTimer
