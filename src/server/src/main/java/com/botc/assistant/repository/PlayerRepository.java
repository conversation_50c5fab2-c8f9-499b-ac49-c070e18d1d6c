package com.botc.assistant.repository;

import com.botc.assistant.model.Player;
import com.google.cloud.firestore.DocumentSnapshot;
import com.google.cloud.firestore.Firestore;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ExecutionException;

/**
 * Repository for managing Player entities in Firestore.
 */
@Repository
public class PlayerRepository {

    private final Firestore firestore;
    private static final String COLLECTION_NAME = "players";

    @Autowired
    public PlayerRepository(@Autowired(required = false) Firestore firestore) {
        this.firestore = firestore;
    }

    /**
     * Save a player to Firestore.
     */
    public Player save(Player player) {
        if (firestore == null) {
            throw new RuntimeException("Firestore is not configured");
        }

        try {
            Map<String, Object> playerData = playerToMap(player);
            firestore.collection(COLLECTION_NAME).document(player.getId()).set(playerData);
            return player;
        } catch (Exception e) {
            throw new RuntimeException("Failed to save player: " + e.getMessage(), e);
        }
    }

    /**
     * Find a player by ID.
     */
    public Optional<Player> findById(String id) {
        if (firestore == null) {
            return Optional.empty();
        }

        try {
            var document = firestore.collection(COLLECTION_NAME).document(id).get().get();
            if (document.exists()) {
                return Optional.of(documentToPlayer(document));
            }
            return Optional.empty();
        } catch (InterruptedException | ExecutionException e) {
            System.err.println("Error finding player by ID: " + e.getMessage());
            return Optional.empty();
        }
    }

    /**
     * Find all players in a game.
     */
    public List<Player> findByGameId(String gameId) {
        if (firestore == null) {
            return new ArrayList<>();
        }

        try {
            List<Player> players = new ArrayList<>();
            var documents = firestore.collection(COLLECTION_NAME)
                    .whereEqualTo("gameId", gameId)
                    .get().get();

            for (var document : documents.getDocuments()) {
                players.add(documentToPlayer(document));
            }

            // Sort by position in memory to avoid needing Firestore composite index
            players.sort((p1, p2) -> Integer.compare(p1.getPosition(), p2.getPosition()));

            return players;
        } catch (InterruptedException | ExecutionException e) {
            System.err.println("Error retrieving players by game ID: " + e.getMessage());
            return new ArrayList<>();
        }
    }

    /**
     * Find players by user ID.
     */
    public List<Player> findByUserId(String userId) {
        if (firestore == null) {
            return new ArrayList<>();
        }

        try {
            List<Player> players = new ArrayList<>();
            var documents = firestore.collection(COLLECTION_NAME)
                    .whereEqualTo("userId", userId)
                    .get().get();

            for (var document : documents.getDocuments()) {
                players.add(documentToPlayer(document));
            }

            return players;
        } catch (InterruptedException | ExecutionException e) {
            System.err.println("Error retrieving players by user ID: " + e.getMessage());
            return new ArrayList<>();
        }
    }

    /**
     * Find alive players in a game.
     */
    public List<Player> findAliveByGameId(String gameId) {
        if (firestore == null) {
            return new ArrayList<>();
        }

        try {
            List<Player> players = new ArrayList<>();
            var documents = firestore.collection(COLLECTION_NAME)
                    .whereEqualTo("gameId", gameId)
                    .whereEqualTo("alive", true)
                    .get().get();

            for (var document : documents.getDocuments()) {
                players.add(documentToPlayer(document));
            }

            // Sort by position in memory to avoid needing Firestore composite index
            players.sort((p1, p2) -> Integer.compare(p1.getPosition(), p2.getPosition()));

            return players;
        } catch (InterruptedException | ExecutionException e) {
            System.err.println("Error retrieving alive players: " + e.getMessage());
            return new ArrayList<>();
        }
    }

    /**
     * Delete a player by ID.
     */
    public void deleteById(String id) {
        if (firestore == null) {
            return;
        }

        try {
            firestore.collection(COLLECTION_NAME).document(id).delete();
        } catch (Exception e) {
            System.err.println("Error deleting player: " + e.getMessage());
        }
    }

    /**
     * Delete all players in a game.
     */
    public void deleteByGameId(String gameId) {
        if (firestore == null) {
            return;
        }

        try {
            var documents = firestore.collection(COLLECTION_NAME)
                    .whereEqualTo("gameId", gameId)
                    .get().get();

            for (var document : documents.getDocuments()) {
                document.getReference().delete();
            }
        } catch (InterruptedException | ExecutionException e) {
            System.err.println("Error deleting players by game ID: " + e.getMessage());
        }
    }

    /**
     * Update specific fields of a player.
     */
    public void updateFields(String id, Map<String, Object> updates) {
        if (firestore == null) {
            return;
        }

        try {
            updates.put("lastActivity", LocalDateTime.now().toString());
            firestore.collection(COLLECTION_NAME).document(id).update(updates);
        } catch (Exception e) {
            System.err.println("Error updating player fields: " + e.getMessage());
        }
    }

    /**
     * Convert Player object to Firestore document map.
     */
    private Map<String, Object> playerToMap(Player player) {
        Map<String, Object> data = new HashMap<>();
        data.put("id", player.getId());
        data.put("name", player.getName());
        data.put("userId", player.getUserId());
        data.put("gameId", player.getGameId());
        data.put("position", player.getPosition());
        data.put("roleId", player.getRoleId());
        data.put("alive", player.isAlive());
        data.put("canVote", player.canVote());
        data.put("nominated", player.isNominated());
        data.put("hasVoted", player.hasVoted());
        data.put("status", player.getStatus().name());
        data.put("neighbors", player.getNeighbors());
        data.put("notes", player.getNotes());
        data.put("joinedAt", player.getJoinedAt().toString());
        data.put("lastActivity", player.getLastActivity().toString());

        return data;
    }

    /**
     * Convert Firestore document to Player object.
     */
    @SuppressWarnings("unchecked")
    private Player documentToPlayer(DocumentSnapshot document) {
        Player player = new Player();

        player.setId(document.getString("id"));
        player.setName(document.getString("name"));
        player.setUserId(document.getString("userId"));
        player.setGameId(document.getString("gameId"));

        Long position = document.getLong("position");
        if (position != null) {
            player.setPosition(position.intValue());
        }

        player.setRoleId(document.getString("roleId"));

        Boolean alive = document.getBoolean("alive");
        if (alive != null) {
            player.setAlive(alive);
        }

        Boolean canVote = document.getBoolean("canVote");
        if (canVote != null) {
            player.setCanVote(canVote);
        }

        Boolean nominated = document.getBoolean("nominated");
        if (nominated != null) {
            player.setNominated(nominated);
        }

        Boolean hasVoted = document.getBoolean("hasVoted");
        if (hasVoted != null) {
            player.setHasVoted(hasVoted);
        }

        String statusStr = document.getString("status");
        if (statusStr != null) {
            player.setStatus(Player.PlayerStatus.valueOf(statusStr));
        }

        List<String> neighbors = (List<String>) document.get("neighbors");
        if (neighbors != null) {
            player.setNeighbors(neighbors);
        }

        player.setNotes(document.getString("notes"));

        String joinedAtStr = document.getString("joinedAt");
        if (joinedAtStr != null) {
            player.setJoinedAt(LocalDateTime.parse(joinedAtStr));
        }

        String lastActivityStr = document.getString("lastActivity");
        if (lastActivityStr != null) {
            player.setLastActivity(LocalDateTime.parse(lastActivityStr));
        }

        return player;
    }
}
