package com.botc.assistant.roles.minions;

/**
 * Poisoner - <PERSON>on role
 * 
 * Each night, choose a player: they are poisoned tonight and tomorrow day.
 * 
 * The Poisoner can disable other players' abilities.
 */
public class Poisoner extends Minion {
    
    public static final String ROLE_ID = "poisoner";
    public static final String ROLE_NAME = "Poisoner";
    public static final String ROLE_DESCRIPTION = "Each night, choose a player: they are poisoned tonight and tomorrow day.";
    
    private Poisoner(Builder builder) {
        super(builder);
    }
    
    public static Builder builder() {
        return new Builder();
    }
    
    public static Poisoner create() {
        return builder().build();
    }
    
    /**
     * Builder for Poisoner role
     */
    public static class Builder extends Minion.Builder<Builder> {
        
        public Builder() {
            id(ROLE_ID);
            name(ROLE_NAME);
            description(ROLE_DESCRIPTION);
            hasNightAbility(true);
            nightPriority(5); // Early in night order
            setupInfo("Poisons a player each night, disabling their ability");
        }
        
        @Override
        protected Builder self() {
            return this;
        }
        
        @Override
        public Poisoner build() {
            return new Poisoner(this);
        }
    }
}
