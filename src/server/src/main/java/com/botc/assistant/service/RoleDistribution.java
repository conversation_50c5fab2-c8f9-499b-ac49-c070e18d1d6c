package com.botc.assistant.service;

/**
 * Represents the distribution of roles for a Blood on the Clocktower game.
 * Contains the count of each role type needed for a specific player count.
 */
public class RoleDistribution {
    private final int townsfolkCount;
    private final int outsiderCount;
    private final int minionCount;
    private final int demonCount;
    
    public RoleDistribution(int townsfolkCount, int outsiderCount, int minionCount, int demonCount) {
        this.townsfolkCount = townsfolkCount;
        this.outsiderCount = outsiderCount;
        this.minionCount = minionCount;
        this.demonCount = demonCount;
    }
    
    public int getTownsfolkCount() { 
        return townsfolkCount; 
    }
    
    public int getOutsiderCount() { 
        return outsiderCount; 
    }
    
    public int getMinionCount() { 
        return minionCount; 
    }
    
    public int getDemonCount() { 
        return demonCount; 
    }
    
    public int getTotalCount() { 
        return townsfolkCount + outsiderCount + minionCount + demonCount; 
    }
    
    @Override
    public String toString() {
        return String.format("RoleDistribution{townsfolk=%d, outsiders=%d, minions=%d, demons=%d}", 
                           townsfolkCount, outsiderCount, minionCount, demonCount);
    }
}
