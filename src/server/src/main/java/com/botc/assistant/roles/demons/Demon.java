package com.botc.assistant.roles.demons;

import com.botc.assistant.roles.AbstractRole;
import com.botc.assistant.roles.RoleTeam;
import com.botc.assistant.roles.RoleType;

/**
 * Abstract base class for all Demon roles.
 * Demons are evil team members who kill at night.
 */
public abstract class Demon extends AbstractRole {
    
    protected Demon(Builder<?> builder) {
        super(builder);
    }
    
    /**
     * Builder for Demon roles
     */
    public abstract static class Builder<T extends Builder<T>> extends AbstractRole.Builder<T> {
        
        public Builder() {
            // Set default values for all Demons
            team(RoleTeam.EVIL);
            type(RoleType.DEMON);
            canBeTargeted(true);
            hasNightAbility(true); // Most demons kill at night
        }
        
        @Override
        public abstract Demon build();
    }
}
