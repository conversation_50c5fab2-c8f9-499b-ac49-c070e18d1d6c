name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  test-frontend:
    name: Test Frontend
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: src/client/package-lock.json

    - name: Install frontend dependencies
      working-directory: ./src/client
      run: npm ci

    - name: Run frontend linting
      working-directory: ./src/client
      run: npm run lint

    - name: Build frontend
      working-directory: ./src/client
      run: npm run build

  test-backend:
    name: Test Backend
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Java
      uses: actions/setup-java@v4
      with:
        java-version: '17'
        distribution: 'temurin'
        cache: maven
        
    - name: Run backend tests
      working-directory: ./src/server
      run: ./mvnw test

    - name: Build backend
      working-directory: ./src/server
      run: ./mvnw clean package -DskipTests

  build-and-push:
    name: Build and Push Docker Image
    runs-on: ubuntu-latest
    needs: [test-frontend, test-backend]
    if: github.ref == 'refs/heads/main'

    permissions:
      contents: read
      packages: write

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Docker Buildx
      uses: docker/setup-buildx-action@v3
      with:
        # Simplified BuildKit configuration for faster builds
        buildkitd-config-inline: |
          [worker.oci]
            max-parallelism = 2
          [worker.containerd]
            max-parallelism = 2

    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Generate lowercase image name
      id: image-name
      run: echo "IMAGE_NAME_LOWER=$(echo '${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}' | tr '[:upper:]' '[:lower:]')" >> $GITHUB_OUTPUT

    - name: Set build timestamp
      run: echo "BUILD_TIMESTAMP=$(date -u +'%Y-%m-%dT%H:%M:%SZ')" >> $GITHUB_ENV

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ steps.image-name.outputs.IMAGE_NAME_LOWER }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}
        labels: |
          org.opencontainers.image.title=Blood on the Clocktower Assistant
          org.opencontainers.image.description=Blood on the Clocktower Assistant
          org.opencontainers.image.url=https://github.com/${{ github.repository }}
          org.opencontainers.image.source=https://github.com/${{ github.repository }}
          org.opencontainers.image.version=${{ github.ref_name }}
          org.opencontainers.image.created=${{ env.BUILD_TIMESTAMP }}
          org.opencontainers.image.revision=${{ github.sha }}
          org.opencontainers.image.licenses=MIT

    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        platforms: linux/amd64
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        # Advanced caching strategy
        cache-from: |
          type=gha
          type=registry,ref=${{ steps.image-name.outputs.IMAGE_NAME_LOWER }}:buildcache
        cache-to: |
          type=gha,mode=max
          type=registry,ref=${{ steps.image-name.outputs.IMAGE_NAME_LOWER }}:buildcache,mode=max
        # Build arguments for optimization
        build-args: |
          BUILDKIT_INLINE_CACHE=1
        # Enable advanced BuildKit features
        build-contexts: |
          alpine=docker-image://alpine:3.19
        provenance: false
        sbom: false

  deploy:
    name: Deploy to Server
    runs-on: ubuntu-latest
    needs: build-and-push
    if: github.ref == 'refs/heads/main'

    steps:
    - name: Deploy to server
      uses: appleboy/ssh-action@v1.0.0
      with:
        host: ${{ secrets.DEPLOY_HOST }}
        username: ${{ secrets.DEPLOY_USER }}
        key: ${{ secrets.DEPLOY_KEY }}
        port: ${{ secrets.DEPLOY_PORT || 22 }}
        debug: true
        script: |
          # Debug information
          echo "=== Deployment Debug Info ==="
          echo "Current user: $(whoami)"
          echo "Current directory: $(pwd)"
          echo "Home directory: $HOME"
          echo "Docker version: $(docker --version)"
          echo "Available disk space:"
          df -h /opt/botc-assistant 2>/dev/null || df -h /
          echo "============================="

          # Ensure deployment directory exists and has correct permissions
          sudo mkdir -p /opt/botc-assistant/{data,backups,logs,deploy}
          sudo chown -R botcassistant:botcassistant /opt/botc-assistant

          # Navigate to deployment directory
          cd /opt/botc-assistant

          # Pull the latest image (convert to lowercase)
          IMAGE_NAME_LOWER=$(echo '${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:latest' | tr '[:upper:]' '[:lower:]')
          echo "Pulling image: $IMAGE_NAME_LOWER"
          docker pull "$IMAGE_NAME_LOWER"

          # Run the deployment script
          echo "Running deployment script with image: $IMAGE_NAME_LOWER"
          ./deploy.sh "$IMAGE_NAME_LOWER"
