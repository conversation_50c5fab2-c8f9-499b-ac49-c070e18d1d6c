.game-timer {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  max-width: 300px;
  margin: 0 auto;
  transition: all 0.3s ease;
}

.game-timer.night {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  color: white;
}

.timer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.timer-phase {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
}

.timer-controls {
  display: flex;
  gap: 8px;
}

.timer-btn {
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: background-color 0.2s ease;
}

.timer-btn:hover:not(:disabled) {
  background-color: rgba(0, 0, 0, 0.1);
}

.timer-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.game-timer.night .timer-btn:hover:not(:disabled) {
  background-color: rgba(255, 255, 255, 0.1);
}

.timer-display {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20px;
}

.time-text {
  font-size: 2.5rem;
  font-weight: 700;
  font-family: 'Courier New', monospace;
  position: absolute;
  z-index: 2;
}

.progress-ring {
  position: relative;
}

.progress-svg {
  transform: rotate(-90deg);
}

.progress-background {
  stroke: rgba(0, 0, 0, 0.1);
}

.progress-foreground {
  stroke: #3498db;
  transition: stroke-dashoffset 0.3s ease;
}

.game-timer.night .progress-background {
  stroke: rgba(255, 255, 255, 0.2);
}

.game-timer.night .progress-foreground {
  stroke: #e74c3c;
}

.timer-status {
  text-align: center;
}

.status-indicator {
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
}

.status-indicator.running {
  background-color: #2ecc71;
  color: white;
}

.status-indicator.paused {
  background-color: #f39c12;
  color: white;
}

.status-indicator.finished {
  background-color: #e74c3c;
  color: white;
}
