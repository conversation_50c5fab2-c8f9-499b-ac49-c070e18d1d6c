package com.botc.assistant.config;

import com.google.auth.oauth2.GoogleCredentials;
import com.google.cloud.firestore.Firestore;
import com.google.firebase.FirebaseApp;
import com.google.firebase.FirebaseOptions;
import com.google.firebase.auth.FirebaseAuth;
import com.google.firebase.cloud.FirestoreClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.Resource;

import javax.annotation.PostConstruct;
import java.io.IOException;

/**
 * Firebase configuration for authentication and user management.
 */
@Configuration
public class FirebaseConfig {

    @Value("${firebase.service-account-key:}")
    private String serviceAccountKeyPath;

    @Value("${firebase.project-id:botc-assistant}")
    private String projectId;

    @PostConstruct
    public void initializeFirebase() throws IOException {
        if (FirebaseApp.getApps().isEmpty()) {
            try {
                // Check if service account key is provided via configuration
                if (serviceAccountKeyPath != null && !serviceAccountKeyPath.isEmpty()) {
                    Resource serviceAccountKey = new org.springframework.core.io.ClassPathResource(
                            serviceAccountKeyPath.replace("classpath:", ""));

                    if (serviceAccountKey.exists()) {
                        GoogleCredentials credentials = GoogleCredentials.fromStream(
                                serviceAccountKey.getInputStream());

                        FirebaseOptions options = FirebaseOptions.builder()
                                .setCredentials(credentials)
                                .setProjectId(projectId)
                                .build();

                        FirebaseApp.initializeApp(options);
                        System.out.println("Firebase initialized successfully with project: " + projectId);
                        return;
                    }
                }

                // Try default location: firebase-service-account.json
                Resource defaultServiceAccountKey = new org.springframework.core.io.ClassPathResource(
                        "firebase-service-account.json");
                if (defaultServiceAccountKey.exists()) {
                    GoogleCredentials credentials = GoogleCredentials.fromStream(
                            defaultServiceAccountKey.getInputStream());

                    FirebaseOptions options = FirebaseOptions.builder()
                            .setCredentials(credentials)
                            .setProjectId(projectId)
                            .build();

                    FirebaseApp.initializeApp(options);
                    System.out.println("Firebase initialized successfully with project: " + projectId
                            + " (using default service account key)");
                    return;
                }

                // For demo purposes, don't initialize Firebase
                System.out.println("Warning: Firebase service account key not found. Running in demo mode.");
                System.out.println(
                        "To enable Firebase, add your service account key to src/main/resources/firebase-service-account.json");

            } catch (Exception e) {
                System.out.println("Warning: Failed to initialize Firebase: " + e.getMessage());
                System.out.println("Running in demo mode without Firebase authentication.");
            }
        }
    }

    @Bean
    public FirebaseAuth firebaseAuth() {
        try {
            return FirebaseAuth.getInstance();
        } catch (Exception e) {
            // Return null for demo - in production this would be properly configured
            System.out.println("Warning: Firebase Auth not properly configured");
            return null;
        }
    }

    @Bean
    public Firestore firestore() {
        try {
            return FirestoreClient.getFirestore();
        } catch (Exception e) {
            System.out.println("Warning: Firestore not properly configured");
            return null;
        }
    }
}
