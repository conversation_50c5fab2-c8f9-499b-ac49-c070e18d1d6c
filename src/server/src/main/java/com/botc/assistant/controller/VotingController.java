package com.botc.assistant.controller;

import com.botc.assistant.model.Game;
import com.botc.assistant.model.Player;
import com.botc.assistant.model.Vote;
import com.botc.assistant.service.GameService;
import com.botc.assistant.service.VotingService;
import com.botc.assistant.repository.PlayerRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * REST controller for voting operations.
 */
@RestController
@RequestMapping("/api/games/{gameId}/voting")
@CrossOrigin(origins = { "http://localhost:5173", "http://localhost:5174", "http://localhost:3000" })
public class VotingController {

    @Autowired
    private VotingService votingService;
    
    @Autowired
    private GameService gameService;
    
    @Autowired
    private PlayerRepository playerRepository;

    /**
     * Start a voting session (nominate a player).
     */
    @PostMapping("/start")
    public ResponseEntity<Vote> startVoting(@PathVariable String gameId, 
                                           @RequestBody StartVotingRequest request) {
        try {
            Optional<Game> gameOpt = gameService.findById(gameId);
            if (gameOpt.isEmpty()) {
                return ResponseEntity.notFound().build();
            }
            
            Game game = gameOpt.get();
            
            // Find nominator and nominee
            Optional<Player> nominatorOpt = playerRepository.findById(request.getNominatorId());
            Optional<Player> nomineeOpt = playerRepository.findById(request.getNominatedPlayerId());
            
            if (nominatorOpt.isEmpty() || nomineeOpt.isEmpty()) {
                return ResponseEntity.badRequest().build();
            }
            
            Vote vote = votingService.nominatePlayer(game, nominatorOpt.get(), nomineeOpt.get());
            return ResponseEntity.ok(vote);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Cast a vote in the current voting session.
     */
    @PostMapping("/vote")
    public ResponseEntity<Void> castVote(@PathVariable String gameId, 
                                        @RequestBody CastVoteRequest request) {
        try {
            Optional<Game> gameOpt = gameService.findById(gameId);
            Optional<Player> playerOpt = playerRepository.findById(request.getPlayerId());
            
            if (gameOpt.isEmpty() || playerOpt.isEmpty()) {
                return ResponseEntity.badRequest().build();
            }
            
            Vote.VoteChoice choice = Vote.VoteChoice.valueOf(request.getChoice().toUpperCase());
            votingService.castVote(gameOpt.get(), playerOpt.get(), choice);
            
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * End the current voting session.
     */
    @PostMapping("/end")
    public ResponseEntity<Vote> endVoting(@PathVariable String gameId) {
        try {
            Optional<Game> gameOpt = gameService.findById(gameId);
            if (gameOpt.isEmpty()) {
                return ResponseEntity.notFound().build();
            }
            
            Vote vote = votingService.endVoting(gameOpt.get());
            return ResponseEntity.ok(vote);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Get the current active vote.
     */
    @GetMapping("/current")
    public ResponseEntity<Vote> getCurrentVote(@PathVariable String gameId) {
        try {
            Optional<Vote> vote = votingService.getCurrentVote(gameId);
            return vote.map(ResponseEntity::ok).orElse(ResponseEntity.notFound().build());
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Get all votes for a game.
     */
    @GetMapping
    public ResponseEntity<List<Vote>> getGameVotes(@PathVariable String gameId) {
        try {
            List<Vote> votes = votingService.getGameVotes(gameId);
            return ResponseEntity.ok(votes);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Cancel the current vote.
     */
    @PostMapping("/cancel")
    public ResponseEntity<Void> cancelVote(@PathVariable String gameId) {
        try {
            Optional<Game> gameOpt = gameService.findById(gameId);
            if (gameOpt.isEmpty()) {
                return ResponseEntity.notFound().build();
            }
            
            votingService.cancelCurrentVote(gameOpt.get());
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    // Request DTOs
    public static class StartVotingRequest {
        private String nominatorId;
        private String nominatedPlayerId;

        // Getters and setters
        public String getNominatorId() { return nominatorId; }
        public void setNominatorId(String nominatorId) { this.nominatorId = nominatorId; }
        public String getNominatedPlayerId() { return nominatedPlayerId; }
        public void setNominatedPlayerId(String nominatedPlayerId) { this.nominatedPlayerId = nominatedPlayerId; }
    }

    public static class CastVoteRequest {
        private String playerId;
        private String choice; // FOR, AGAINST, ABSTAIN

        // Getters and setters
        public String getPlayerId() { return playerId; }
        public void setPlayerId(String playerId) { this.playerId = playerId; }
        public String getChoice() { return choice; }
        public void setChoice(String choice) { this.choice = choice; }
    }
}
