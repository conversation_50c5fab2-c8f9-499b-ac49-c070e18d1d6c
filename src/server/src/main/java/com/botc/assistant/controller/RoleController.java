package com.botc.assistant.controller;

import com.botc.assistant.roles.Role;
import com.botc.assistant.service.RoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * REST controller for role management operations.
 */
@RestController
@RequestMapping("/api/roles")
@CrossOrigin(origins = { "http://localhost:5173", "http://localhost:5174", "http://localhost:3000" })
public class RoleController {

    @Autowired
    private RoleService roleService;

    /**
     * Get all available roles.
     */
    @GetMapping
    public ResponseEntity<List<Role>> getAllRoles() {
        try {
            List<Role> roles = roleService.getAllRoles();
            return ResponseEntity.ok(roles);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Get role by ID.
     */
    @GetMapping("/{roleId}")
    public ResponseEntity<Role> getRole(@PathVariable String roleId) {
        try {
            Role role = roleService.getRoleById(roleId).orElseThrow();
            return ResponseEntity.ok(role);
        } catch (Exception e) {
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * Get roles by type.
     */
    @GetMapping("/type/{type}")
    public ResponseEntity<List<Role>> getRolesByType(@PathVariable String type) {
        try {
            List<Role> roles;
            switch (type.toLowerCase()) {
                case "townsfolk":
                    roles = roleService.getTownsfolkRoles();
                    break;
                case "outsider":
                    roles = roleService.getOutsiderRoles();
                    break;
                case "minion":
                    roles = roleService.getMinionRoles();
                    break;
                case "demon":
                    roles = roleService.getDemonRoles();
                    break;
                default:
                    return ResponseEntity.badRequest().build();
            }
            return ResponseEntity.ok(roles);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Get roles with night abilities.
     */
    @GetMapping("/night-abilities")
    public ResponseEntity<List<Role>> getRolesWithNightAbilities() {
        try {
            List<Role> roles = roleService.getRolesWithNightAbilities();
            return ResponseEntity.ok(roles);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Get roles with day abilities.
     */
    @GetMapping("/day-abilities")
    public ResponseEntity<List<Role>> getRolesWithDayAbilities() {
        try {
            List<Role> roles = roleService.getRolesWithDayAbilities();
            return ResponseEntity.ok(roles);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }
}
