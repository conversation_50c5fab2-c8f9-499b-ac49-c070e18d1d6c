import { describe, it, expect, vi } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import RoleCard from './RoleCard'

const mockRole = {
  id: 'chef',
  name: 'Chef',
  description: 'You start knowing how many pairs of evil players there are.',
  team: 'good',
  type: 'townsfolk',
  hasNightAbility: true,
  hasDayAbility: false,
  nightPriority: 1,
  setupInfo: 'Learns how many pairs of evil players are sitting next to each other'
}

const mockEvilRole = {
  id: 'imp',
  name: 'Imp',
  description: 'Each night*, choose a player: they die.',
  team: 'evil',
  type: 'demon',
  hasNightAbility: true,
  hasDayAbility: false,
  nightPriority: 50
}

describe('RoleCard Component', () => {
  it('renders empty card when no role provided', () => {
    render(<RoleCard />)
    
    expect(screen.getByTestId('empty-role-card')).toBeInTheDocument()
    expect(screen.getByText('Rôle Inconnu')).toBeInTheDocument()
    expect(screen.getByText('?')).toBeInTheDocument()
  })

  it('renders role information correctly', () => {
    render(<RoleCard role={mockRole} />)
    
    expect(screen.getByTestId('role-card')).toBeInTheDocument()
    expect(screen.getByTestId('role-name')).toHaveTextContent('Chef')
    expect(screen.getByText('townsfolk')).toBeInTheDocument()
  })

  it('shows team-specific styling for good roles', () => {
    render(<RoleCard role={mockRole} />)
    
    const card = screen.getByTestId('role-card')
    expect(card).toHaveClass('good')
  })

  it('shows team-specific styling for evil roles', () => {
    render(<RoleCard role={mockEvilRole} />)
    
    const card = screen.getByTestId('role-card')
    expect(card).toHaveClass('evil')
  })

  it('displays night ability indicator', () => {
    render(<RoleCard role={mockRole} />)
    
    const nightIndicator = screen.getByTitle('Capacité de nuit')
    expect(nightIndicator).toBeInTheDocument()
    expect(nightIndicator).toHaveTextContent('🌙')
  })

  it('displays day ability indicator when role has day ability', () => {
    const roleWithDayAbility = { ...mockRole, hasDayAbility: true }
    render(<RoleCard role={roleWithDayAbility} />)
    
    const dayIndicator = screen.getByTitle('Capacité de jour')
    expect(dayIndicator).toBeInTheDocument()
    expect(dayIndicator).toHaveTextContent('☀️')
  })

  it('calls onFlip when card is clicked', async () => {
    const onFlip = vi.fn()
    const user = userEvent.setup()
    render(<RoleCard role={mockRole} onFlip={onFlip} />)
    
    const card = screen.getByTestId('role-card')
    await user.click(card)
    
    expect(onFlip).toHaveBeenCalledTimes(1)
  })

  it('shows flipped state when isFlipped is true', () => {
    render(<RoleCard role={mockRole} isFlipped={true} />)
    
    const card = screen.getByTestId('role-card')
    expect(card).toHaveClass('flipped')
  })

  it('shows role description when flipped', () => {
    render(<RoleCard role={mockRole} isFlipped={true} />)
    
    expect(screen.getByTestId('role-description')).toHaveTextContent(mockRole.description)
  })

  it('shows setup info when available and flipped', () => {
    render(<RoleCard role={mockRole} isFlipped={true} />)
    
    expect(screen.getByText(/Setup:/)).toBeInTheDocument()
    expect(screen.getByText(mockRole.setupInfo)).toBeInTheDocument()
  })

  it('shows night priority when available and flipped', () => {
    render(<RoleCard role={mockRole} isFlipped={true} />)
    
    expect(screen.getByText(/Priorité nuit:/)).toBeInTheDocument()
    expect(screen.getByText('1')).toBeInTheDocument()
  })

  it('opens details overlay when details button is clicked', async () => {
    const onToggleDetails = vi.fn()
    const user = userEvent.setup()
    render(<RoleCard role={mockRole} isFlipped={true} onToggleDetails={onToggleDetails} />)
    
    const detailsButton = screen.getByLabelText('Voir les détails du rôle')
    await user.click(detailsButton)
    
    expect(onToggleDetails).toHaveBeenCalledTimes(1)
  })

  it('shows details overlay when showDetails is true', () => {
    render(<RoleCard role={mockRole} showDetails={true} />)
    
    expect(screen.getByTestId('details-overlay')).toBeInTheDocument()
    expect(screen.getByText('Type')).toBeInTheDocument()
    expect(screen.getByText('Description')).toBeInTheDocument()
    expect(screen.getByText('Capacités')).toBeInTheDocument()
  })

  it('closes details overlay when close button is clicked', async () => {
    const onToggleDetails = vi.fn()
    const user = userEvent.setup()
    render(<RoleCard role={mockRole} showDetails={true} onToggleDetails={onToggleDetails} />)
    
    const closeButton = screen.getByLabelText('Fermer les détails')
    await user.click(closeButton)
    
    expect(onToggleDetails).toHaveBeenCalledTimes(1)
  })

  it('prevents event bubbling when details button is clicked', async () => {
    const onFlip = vi.fn()
    const onToggleDetails = vi.fn()
    const user = userEvent.setup()
    render(<RoleCard role={mockRole} isFlipped={true} onFlip={onFlip} onToggleDetails={onToggleDetails} />)
    
    const detailsButton = screen.getByLabelText('Voir les détails du rôle')
    await user.click(detailsButton)
    
    expect(onToggleDetails).toHaveBeenCalledTimes(1)
    expect(onFlip).not.toHaveBeenCalled()
  })

  it('displays correct type icons', () => {
    const townsfolkRole = { ...mockRole, type: 'townsfolk' }
    const { rerender } = render(<RoleCard role={townsfolkRole} />)
    expect(screen.getByText('👥')).toBeInTheDocument()
    
    const outsiderRole = { ...mockRole, type: 'outsider' }
    rerender(<RoleCard role={outsiderRole} />)
    expect(screen.getByText('🚪')).toBeInTheDocument()
    
    const minionRole = { ...mockRole, type: 'minion' }
    rerender(<RoleCard role={minionRole} />)
    expect(screen.getByText('👹')).toBeInTheDocument()
    
    const demonRole = { ...mockRole, type: 'demon' }
    rerender(<RoleCard role={demonRole} />)
    expect(screen.getByText('😈')).toBeInTheDocument()
  })

  it('handles mouse hover events', () => {
    render(<RoleCard role={mockRole} />)
    
    const card = screen.getByTestId('role-card')
    
    fireEvent.mouseEnter(card)
    expect(card).toHaveClass('hovered')
    
    fireEvent.mouseLeave(card)
    expect(card).not.toHaveClass('hovered')
  })

  it('shows passive ability when role has no night or day abilities', () => {
    const passiveRole = { ...mockRole, hasNightAbility: false, hasDayAbility: false }
    render(<RoleCard role={passiveRole} showDetails={true} />)
    
    expect(screen.getByText('Capacité passive')).toBeInTheDocument()
  })

  it('has proper accessibility attributes', () => {
    render(<RoleCard role={mockRole} isFlipped={true} showDetails={true} />)
    
    expect(screen.getByLabelText('Voir les détails du rôle')).toBeInTheDocument()
    expect(screen.getByLabelText('Fermer les détails')).toBeInTheDocument()
  })
})
