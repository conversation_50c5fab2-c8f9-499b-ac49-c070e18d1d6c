import { useState } from 'react'
import './RoleCard.css'

const RoleCard = ({ 
  role,
  isFlipped = false,
  onFlip = () => {},
  showDetails = false,
  onToggleDetails = () => {}
}) => {
  const [isHovered, setIsHovered] = useState(false)

  if (!role) {
    return (
      <div className="role-card empty" data-testid="empty-role-card">
        <div className="card-content">
          <div className="role-placeholder">?</div>
          <p className="role-name">Rôle Inconnu</p>
        </div>
      </div>
    )
  }

  const getTeamColor = (team) => {
    switch (team?.toLowerCase()) {
      case 'good': return '#2ecc71'
      case 'evil': return '#e74c3c'
      default: return '#95a5a6'
    }
  }

  const getTypeIcon = (type) => {
    switch (type?.toLowerCase()) {
      case 'townsfolk': return '👥'
      case 'outsider': return '🚪'
      case 'minion': return '👹'
      case 'demon': return '😈'
      default: return '❓'
    }
  }

  const handleCardClick = () => {
    if (onFlip) {
      onFlip()
    }
  }

  const handleDetailsClick = (e) => {
    e.stopPropagation()
    onToggleDetails()
  }

  return (
    <div 
      className={`role-card ${role.team?.toLowerCase()} ${isFlipped ? 'flipped' : ''} ${isHovered ? 'hovered' : ''}`}
      data-testid="role-card"
      onClick={handleCardClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      style={{ '--team-color': getTeamColor(role.team) }}
    >
      <div className="card-inner">
        {/* Front of card */}
        <div className="card-front">
          <div className="card-header">
            <div className="role-type-icon">{getTypeIcon(role.type)}</div>
            <div className="team-indicator" style={{ backgroundColor: getTeamColor(role.team) }}></div>
          </div>
          <div className="card-content">
            <h3 className="role-name" data-testid="role-name">{role.name}</h3>
            <p className="role-type">{role.type}</p>
            {role.hasNightAbility && (
              <div className="ability-indicator night" title="Capacité de nuit">
                🌙
              </div>
            )}
            {role.hasDayAbility && (
              <div className="ability-indicator day" title="Capacité de jour">
                ☀️
              </div>
            )}
          </div>
        </div>

        {/* Back of card */}
        <div className="card-back">
          <div className="card-header">
            <button 
              className="details-btn"
              onClick={handleDetailsClick}
              aria-label="Voir les détails du rôle"
            >
              ℹ️
            </button>
          </div>
          <div className="card-content">
            <h3 className="role-name">{role.name}</h3>
            <p className="role-description" data-testid="role-description">
              {role.description}
            </p>
            {role.setupInfo && (
              <div className="setup-info">
                <strong>Setup:</strong> {role.setupInfo}
              </div>
            )}
            {role.nightPriority && (
              <div className="night-priority">
                <strong>Priorité nuit:</strong> {role.nightPriority}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Details modal overlay */}
      {showDetails && (
        <div className="details-overlay" data-testid="details-overlay">
          <div className="details-content">
            <div className="details-header">
              <h2>{role.name}</h2>
              <button 
                className="close-btn"
                onClick={handleDetailsClick}
                aria-label="Fermer les détails"
              >
                ✕
              </button>
            </div>
            <div className="details-body">
              <div className="detail-section">
                <h4>Type</h4>
                <p>{role.type} ({role.team})</p>
              </div>
              <div className="detail-section">
                <h4>Description</h4>
                <p>{role.description}</p>
              </div>
              {role.setupInfo && (
                <div className="detail-section">
                  <h4>Information de Setup</h4>
                  <p>{role.setupInfo}</p>
                </div>
              )}
              <div className="detail-section">
                <h4>Capacités</h4>
                <ul>
                  {role.hasNightAbility && <li>Capacité de nuit</li>}
                  {role.hasDayAbility && <li>Capacité de jour</li>}
                  {!role.hasNightAbility && !role.hasDayAbility && <li>Capacité passive</li>}
                </ul>
              </div>
              {role.nightPriority && (
                <div className="detail-section">
                  <h4>Priorité de Nuit</h4>
                  <p>{role.nightPriority}</p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default RoleCard
