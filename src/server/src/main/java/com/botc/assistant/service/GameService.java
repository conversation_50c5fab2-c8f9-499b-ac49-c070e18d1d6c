package com.botc.assistant.service;

import com.botc.assistant.model.Game;
import com.botc.assistant.model.Player;
import com.botc.assistant.model.User;
import com.botc.assistant.repository.GameRepository;
import com.botc.assistant.repository.PlayerRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * Service for managing Blood on the Clock Tower games.
 * Handles game creation, player management, and game state transitions.
 */
@Service
public class GameService {

    private final GameRepository gameRepository;
    private final PlayerRepository playerRepository;
    private final UserService userService;

    @Autowired
    public GameService(GameRepository gameRepository,
            PlayerRepository playerRepository,
            UserService userService) {
        this.gameRepository = gameRepository;
        this.playerRepository = playerRepository;
        this.userService = userService;
    }

    /**
     * Create a new game.
     */
    public Game createGame(String name, int maxPlayers, String storytellerId) {
        Optional<User> storytellerOpt = userService.findById(storytellerId);
        User storyteller;

        if (storytellerOpt.isEmpty()) {
            // In demo mode, create a mock storyteller
            System.out.println("Demo mode: Creating mock storyteller for ID: " + storytellerId);
            storyteller = new User(storytellerId, "Demo Storyteller", "<EMAIL>");
            storyteller.setUserRole(User.UserRole.STORYTELLER);
        } else {
            storyteller = storytellerOpt.get();
            if (!storyteller.isStoryteller()) {
                throw new IllegalArgumentException("User is not a storyteller");
            }
        }

        Game game = new Game(name, maxPlayers, storyteller);
        return gameRepository.save(game);
    }

    /**
     * Find a game by ID.
     */
    public Optional<Game> findById(String id) {
        Optional<Game> gameOpt = gameRepository.findById(id);
        if (gameOpt.isPresent()) {
            Game game = gameOpt.get();
            // Load players and storyteller
            loadGameDetails(game);
            return Optional.of(game);
        }
        return Optional.empty();
    }

    /**
     * Find a game by join code.
     */
    public Optional<Game> findByCode(String code) {
        Optional<Game> gameOpt = gameRepository.findByCode(code);
        if (gameOpt.isPresent()) {
            Game game = gameOpt.get();
            loadGameDetails(game);
            return Optional.of(game);
        }
        return Optional.empty();
    }

    /**
     * Get all active games.
     */
    public List<Game> getAllActiveGames() {
        List<Game> games = gameRepository.findAllActive();
        for (Game game : games) {
            loadGameDetails(game);
        }
        return games;
    }

    /**
     * Get games by storyteller.
     */
    public List<Game> getGamesByStoryteller(String storytellerId) {
        List<Game> games = gameRepository.findByStorytellerId(storytellerId);
        for (Game game : games) {
            loadGameDetails(game);
        }
        return games;
    }

    /**
     * Add a player to a game.
     */
    public Player addPlayer(String gameId, String userId) {
        Optional<Game> gameOpt = findById(gameId);
        if (gameOpt.isEmpty()) {
            throw new IllegalArgumentException("Game not found");
        }

        Game game = gameOpt.get();
        if (game.getState() != Game.GameState.WAITING) {
            throw new IllegalStateException("Cannot join a game that has already started");
        }

        if (game.getCurrentPlayers() >= game.getMaxPlayers()) {
            throw new IllegalStateException("Game is full");
        }

        // Check if user is already in the game
        List<Player> existingPlayers = playerRepository.findByGameId(gameId);
        boolean userAlreadyInGame = existingPlayers.stream()
                .anyMatch(p -> userId.equals(p.getUserId()));

        if (userAlreadyInGame) {
            throw new IllegalStateException("User is already in this game");
        }

        // Get user information to use their account name
        Optional<User> userOpt = userService.findById(userId);
        String playerName;
        if (userOpt.isPresent()) {
            playerName = userOpt.get().getUsername();
        } else {
            // Fallback for demo mode
            playerName = "Player " + (existingPlayers.size() + 1);
        }

        // Create new player
        int position = existingPlayers.size();
        Player player = new Player(playerName, position, gameId);
        player.setUserId(userId);

        // Save player
        player = playerRepository.save(player);

        // Update game player count
        game.setCurrentPlayers(existingPlayers.size() + 1);
        gameRepository.save(game);

        return player;
    }

    /**
     * Remove a player from a game.
     */
    public void removePlayer(String gameId, String playerId) {
        Optional<Game> gameOpt = findById(gameId);
        if (gameOpt.isEmpty()) {
            throw new IllegalArgumentException("Game not found");
        }

        Game game = gameOpt.get();
        if (game.getState() != Game.GameState.WAITING) {
            throw new IllegalStateException("Cannot remove players from a game that has started");
        }

        playerRepository.deleteById(playerId);

        // Update game player count
        List<Player> remainingPlayers = playerRepository.findByGameId(gameId);
        game.setCurrentPlayers(remainingPlayers.size());
        gameRepository.save(game);
    }

    /**
     * Start a game.
     */
    public Game startGame(String gameId, String storytellerId) {
        Optional<Game> gameOpt = findById(gameId);
        if (gameOpt.isEmpty()) {
            throw new IllegalArgumentException("Game not found");
        }

        Game game = gameOpt.get();
        if (!game.isStoryteller(storytellerId)) {
            throw new IllegalArgumentException("Only the storyteller can start the game");
        }

        if (game.getCurrentPlayers() < 5) {
            throw new IllegalStateException("Need at least 5 players to start the game");
        }

        game.startGame();
        return gameRepository.save(game);
    }

    /**
     * End a game.
     */
    public Game endGame(String gameId, String storytellerId) {
        Optional<Game> gameOpt = findById(gameId);
        if (gameOpt.isEmpty()) {
            throw new IllegalArgumentException("Game not found");
        }

        Game game = gameOpt.get();
        if (!game.isStoryteller(storytellerId)) {
            throw new IllegalArgumentException("Only the storyteller can end the game");
        }

        game.endGame();
        return gameRepository.save(game);
    }

    /**
     * Delete a game and all associated data.
     */
    public void deleteGame(String gameId, String storytellerId) {
        Optional<Game> gameOpt = findById(gameId);
        if (gameOpt.isEmpty()) {
            throw new IllegalArgumentException("Game not found");
        }

        Game game = gameOpt.get();
        if (!game.isStoryteller(storytellerId)) {
            throw new IllegalArgumentException("Only the storyteller can delete the game");
        }

        // Delete all players
        playerRepository.deleteByGameId(gameId);

        // Delete the game
        gameRepository.deleteById(gameId);
    }

    /**
     * Update game phase.
     */
    public Game updatePhase(String gameId, Game.GamePhase newPhase, String storytellerId) {
        Optional<Game> gameOpt = findById(gameId);
        if (gameOpt.isEmpty()) {
            throw new IllegalArgumentException("Game not found");
        }

        Game game = gameOpt.get();
        if (!game.isStoryteller(storytellerId)) {
            throw new IllegalArgumentException("Only the storyteller can change game phase");
        }

        game.setCurrentPhase(newPhase);
        return gameRepository.save(game);
    }

    /**
     * Load game details (players and storyteller).
     */
    private void loadGameDetails(Game game) {
        // Load players
        List<Player> players = playerRepository.findByGameId(game.getId());
        game.setPlayers(players);

        // Load storyteller
        if (game.getStorytellerId() != null) {
            userService.findById(game.getStorytellerId())
                    .ifPresent(game::setStoryteller);
        }
    }
}
