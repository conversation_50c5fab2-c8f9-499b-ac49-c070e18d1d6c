package com.botc.assistant.roles.outsiders;

/**
 * Drunk - Outsider role
 * 
 * You do not know you are the Drunk. You think you are a Townsfolk character, but you are not.
 * 
 * The Drunk thinks they are a Townsfolk but their ability doesn't work.
 */
public class Drunk extends Outsider {
    
    public static final String ROLE_ID = "drunk";
    public static final String ROLE_NAME = "Drunk";
    public static final String ROLE_DESCRIPTION = "You do not know you are the Drunk. You think you are a Townsfolk character, but you are not.";
    
    private Drunk(Builder builder) {
        super(builder);
    }
    
    public static Builder builder() {
        return new Builder();
    }
    
    public static Drunk create() {
        return builder().build();
    }
    
    /**
     * Builder for Drunk role
     */
    public static class Builder extends Outsider.Builder<Builder> {
        
        public Builder() {
            id(ROLE_ID);
            name(ROLE_NAME);
            description(ROLE_DESCRIPTION);
            setupInfo("Thinks they are a Townsfolk but their ability doesn't work");
        }
        
        @Override
        protected Builder self() {
            return this;
        }
        
        @Override
        public Drunk build() {
            return new Drunk(this);
        }
    }
}
