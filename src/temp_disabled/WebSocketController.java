package com.botc.assistant.controller;

import com.botc.assistant.model.Game;
import com.botc.assistant.model.Player;
import com.botc.assistant.model.Vote;
import com.botc.assistant.service.GameService;
import com.botc.assistant.service.NotificationService;
import com.botc.assistant.service.PhaseService;
import com.botc.assistant.service.VotingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.DestinationVariable;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.messaging.simp.SimpMessageHeaderAccessor;
import org.springframework.stereotype.Controller;

import java.security.Principal;
import java.util.Map;
import java.util.Optional;

/**
 * WebSocket controller for handling real-time game events.
 * Processes messages from clients and broadcasts updates to all connected players.
 */
@Controller
public class WebSocketController {

    private final GameService gameService;
    private final VotingService votingService;
    private final PhaseService phaseService;
    private final NotificationService notificationService;

    @Autowired
    public WebSocketController(GameService gameService, 
                              VotingService votingService,
                              PhaseService phaseService,
                              NotificationService notificationService) {
        this.gameService = gameService;
        this.votingService = votingService;
        this.phaseService = phaseService;
        this.notificationService = notificationService;
    }

    /**
     * Handle player joining a game via WebSocket.
     */
    @MessageMapping("/game/{gameId}/join")
    public void joinGame(@DestinationVariable String gameId, 
                        @Payload Map<String, Object> payload,
                        Principal principal,
                        SimpMessageHeaderAccessor headerAccessor) {
        try {
            String playerName = (String) payload.get("playerName");
            
            // Get game and add player
            Optional<Game> gameOpt = gameService.findById(gameId);
            if (gameOpt.isEmpty()) {
                // Send error to user
                return;
            }

            Game game = gameOpt.get();
            // Add player logic would go here
            // This is a simplified version
            
        } catch (Exception e) {
            // Handle error
        }
    }

    /**
     * Handle voting via WebSocket.
     */
    @MessageMapping("/game/{gameId}/vote")
    public void castVote(@DestinationVariable String gameId,
                        @Payload Map<String, Object> payload,
                        Principal principal) {
        try {
            String playerId = (String) payload.get("playerId");
            String voteChoice = (String) payload.get("choice");
            
            Optional<Game> gameOpt = gameService.findById(gameId);
            if (gameOpt.isEmpty()) {
                return;
            }

            Game game = gameOpt.get();
            Player player = game.getPlayers().stream()
                    .filter(p -> p.getId().equals(playerId))
                    .findFirst()
                    .orElse(null);

            if (player == null) {
                return;
            }

            Vote.VoteChoice choice = Vote.VoteChoice.valueOf(voteChoice.toUpperCase());
            votingService.castVote(game, player, choice);
            
        } catch (Exception e) {
            // Handle error
        }
    }

    /**
     * Handle nomination via WebSocket.
     */
    @MessageMapping("/game/{gameId}/nominate")
    public void nominatePlayer(@DestinationVariable String gameId,
                              @Payload Map<String, Object> payload,
                              Principal principal) {
        try {
            String nominatorId = (String) payload.get("nominatorId");
            String nomineeId = (String) payload.get("nomineeId");
            
            Optional<Game> gameOpt = gameService.findById(gameId);
            if (gameOpt.isEmpty()) {
                return;
            }

            Game game = gameOpt.get();
            
            Player nominator = game.getPlayers().stream()
                    .filter(p -> p.getId().equals(nominatorId))
                    .findFirst()
                    .orElse(null);
                    
            Player nominee = game.getPlayers().stream()
                    .filter(p -> p.getId().equals(nomineeId))
                    .findFirst()
                    .orElse(null);

            if (nominator == null || nominee == null) {
                return;
            }

            votingService.nominatePlayer(game, nominator, nominee);
            
        } catch (Exception e) {
            // Handle error
        }
    }

    /**
     * Handle phase transition via WebSocket (storyteller only).
     */
    @MessageMapping("/game/{gameId}/phase/next")
    public void nextPhase(@DestinationVariable String gameId,
                         Principal principal) {
        try {
            Optional<Game> gameOpt = gameService.findById(gameId);
            if (gameOpt.isEmpty()) {
                return;
            }

            Game game = gameOpt.get();
            
            // Check if user is the storyteller
            // This would need proper authentication check
            
            phaseService.transitionToNextPhase(game);
            
        } catch (Exception e) {
            // Handle error
        }
    }

    /**
     * Handle starting voting via WebSocket (storyteller only).
     */
    @MessageMapping("/game/{gameId}/voting/start")
    public void startVoting(@DestinationVariable String gameId,
                           Principal principal) {
        try {
            Optional<Game> gameOpt = gameService.findById(gameId);
            if (gameOpt.isEmpty()) {
                return;
            }

            Game game = gameOpt.get();
            
            // Check if user is the storyteller
            // This would need proper authentication check
            
            votingService.startVoting(game);
            
        } catch (Exception e) {
            // Handle error
        }
    }

    /**
     * Handle ending voting via WebSocket (storyteller only).
     */
    @MessageMapping("/game/{gameId}/voting/end")
    public void endVoting(@DestinationVariable String gameId,
                         Principal principal) {
        try {
            Optional<Game> gameOpt = gameService.findById(gameId);
            if (gameOpt.isEmpty()) {
                return;
            }

            Game game = gameOpt.get();
            
            // Check if user is the storyteller
            // This would need proper authentication check
            
            votingService.endVoting(game);
            
        } catch (Exception e) {
            // Handle error
        }
    }

    /**
     * Handle role action via WebSocket.
     */
    @MessageMapping("/game/{gameId}/role/action")
    public void roleAction(@DestinationVariable String gameId,
                          @Payload Map<String, Object> payload,
                          Principal principal) {
        try {
            String roleId = (String) payload.get("roleId");
            String actionData = (String) payload.get("actionData");
            
            Optional<Game> gameOpt = gameService.findById(gameId);
            if (gameOpt.isEmpty()) {
                return;
            }

            Game game = gameOpt.get();
            
            // Process the role action
            phaseService.processNightAction(game, roleId, actionData);
            
        } catch (Exception e) {
            // Handle error
        }
    }

    /**
     * Handle chat message via WebSocket.
     */
    @MessageMapping("/game/{gameId}/chat")
    public void sendChatMessage(@DestinationVariable String gameId,
                               @Payload Map<String, Object> payload,
                               Principal principal) {
        try {
            String message = (String) payload.get("message");
            String playerId = (String) payload.get("playerId");
            
            // Validate and sanitize message
            if (message == null || message.trim().isEmpty() || message.length() > 500) {
                return;
            }

            Optional<Game> gameOpt = gameService.findById(gameId);
            if (gameOpt.isEmpty()) {
                return;
            }

            Game game = gameOpt.get();
            Player player = game.getPlayers().stream()
                    .filter(p -> p.getId().equals(playerId))
                    .findFirst()
                    .orElse(null);

            if (player == null) {
                return;
            }

            // Broadcast chat message
            notificationService.sendCustomMessage(gameId, 
                String.format("%s: %s", player.getName(), message.trim()), 
                "chat.message");
            
        } catch (Exception e) {
            // Handle error
        }
    }

    /**
     * Handle player connection status updates.
     */
    @MessageMapping("/game/{gameId}/heartbeat")
    public void heartbeat(@DestinationVariable String gameId,
                         @Payload Map<String, Object> payload,
                         Principal principal) {
        try {
            String playerId = (String) payload.get("playerId");
            
            // Update player's last activity
            // This would be used to track connection status
            
        } catch (Exception e) {
            // Handle error
        }
    }
}
