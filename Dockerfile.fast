# Fast build Dockerfile for development and testing
# Simplified single-stage build for faster iteration

FROM eclipse-temurin:17-jdk-alpine

# Install Node.js and npm
RUN apk add --no-cache nodejs npm curl

# Set working directory
WORKDIR /app

# Copy and build frontend first (smaller, faster)
COPY src/client/package*.json ./client/
WORKDIR /app/client
RUN npm ci --omit=dev --prefer-offline --no-audit --no-fund

COPY src/client/ ./
RUN npm run build

# Copy and build backend
WORKDIR /app/server
COPY src/server/pom.xml ./
COPY src/server/mvnw ./
COPY src/server/.mvn ./.mvn

# Download dependencies with faster settings
RUN ./mvnw dependency:resolve -B -T 2C -Dmaven.artifact.threads=5

# Copy source and build
COPY src/server/src ./src
RUN ./mvnw clean package -DskipTests -B -T 2C

# Setup runtime
WORKDIR /app
RUN cp server/target/*.jar app.jar
RUN cp -r client/dist static

# Create app user
RUN addgroup -g 1001 -S appuser && adduser -S appuser -G appuser -u 1001
RUN mkdir -p /app/data && chown -R appuser:appuser /app

USER appuser
EXPOSE 8080

# Environment variables
ENV SPRING_PROFILES_ACTIVE=production \
    SERVER_PORT=8080 \
    SPRING_DATASOURCE_URL=***************************** \
    JAVA_OPTS="-XX:+UseContainerSupport -XX:MaxRAMPercentage=75.0"

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
  CMD curl -f http://localhost:8080/actuator/health || exit 1

# Run the application
ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -jar /app/app.jar"]
