# Blood on the Clocktower Assistant Configuration

# Server Configuration
server.port=8080
server.servlet.context-path=/

# Firebase Configuration
firebase.service-account-key=classpath:firebase-service-account.json
firebase.project-id=botc-assistant

# Jackson Configuration
spring.jackson.serialization.fail-on-empty-beans=false

# Static Resources Configuration
spring.web.resources.static-locations=file:./static/,classpath:/static/
spring.web.resources.cache.period=********
spring.mvc.static-path-pattern=/**

# Logging Configuration
logging.level.com.botc.assistant=DEBUG
logging.level.org.springframework.security=DEBUG
logging.level.org.springframework.web=INFO

# Session Configuration
server.servlet.session.timeout=30m
server.servlet.session.cookie.http-only=true
server.servlet.session.cookie.secure=false

# WebSocket Configuration
spring.websocket.sockjs.heartbeat-time=25000
spring.websocket.sockjs.disconnect-delay=5000

# Application Specific Configuration
botc.game.max-players=20
botc.game.min-players=5
botc.game.default-timeout=300
botc.game.cleanup-interval=3600

# Production Profile Configuration
---
spring.config.activate.on-profile=production
logging.level.org.springframework=INFO
logging.level.com.botc.assistant=INFO

# Production Firebase Configuration
firebase.service-account-key=${FIREBASE_SERVICE_ACCOUNT_KEY:classpath:firebase-service-account.json}
firebase.project-id=${FIREBASE_PROJECT_ID:botc-assistant}
