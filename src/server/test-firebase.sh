#!/bin/bash

# Test script for Firebase Authentication integration
# This script tests user registration and authentication

echo "🔥 Testing Firebase Authentication Integration"
echo "=============================================="

# Start the server in background
echo "Starting server..."
./start-server.sh &
SERVER_PID=$!

# Wait for server to start
echo "Waiting for server to start..."
sleep 10

# Test user registration
echo ""
echo "📝 Testing user registration..."
curl -X POST http://localhost:8080/api/users/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "email": "<EMAIL>", 
    "password": "password123"
  }' \
  -w "\nHTTP Status: %{http_code}\n"

echo ""
echo "📝 Testing storyteller registration..."
curl -X POST http://localhost:8080/api/users/register-storyteller \
  -H "Content-Type: application/json" \
  -d '{
    "username": "storyteller",
    "email": "<EMAIL>",
    "password": "password123"
  }' \
  -w "\nHTTP Status: %{http_code}\n"

echo ""
echo "📋 Testing user list..."
curl -X GET http://localhost:8080/api/users \
  -w "\nHTTP Status: %{http_code}\n"

# Clean up
echo ""
echo "🧹 Cleaning up..."
kill $SERVER_PID
echo "Test completed!"
