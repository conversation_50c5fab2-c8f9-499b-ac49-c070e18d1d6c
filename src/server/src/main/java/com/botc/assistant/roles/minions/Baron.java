package com.botc.assistant.roles.minions;

/**
 * Baron - Minion role
 * 
 * There are extra Outsiders in play. [+2 Outsiders]
 * 
 * The Baron adds extra Outsiders to the game setup.
 */
public class Baron extends Minion {
    
    public static final String ROLE_ID = "baron";
    public static final String ROLE_NAME = "Baron";
    public static final String ROLE_DESCRIPTION = "There are extra Outsiders in play. [+2 Outsiders]";
    
    private Baron(Builder builder) {
        super(builder);
    }
    
    public static Builder builder() {
        return new Builder();
    }
    
    public static Baron create() {
        return builder().build();
    }
    
    /**
     * Builder for Baron role
     */
    public static class Builder extends Minion.Builder<Builder> {
        
        public Builder() {
            id(ROLE_ID);
            name(ROLE_NAME);
            description(ROLE_DESCRIPTION);
            setupInfo("Adds 2 extra Outsiders to the game setup");
        }
        
        @Override
        protected Builder self() {
            return this;
        }
        
        @Override
        public Baron build() {
            return new Baron(this);
        }
    }
}
