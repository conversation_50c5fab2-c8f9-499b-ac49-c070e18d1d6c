// Firebase Authentication service functions
import { 
  createUserWithEmailAndPassword, 
  signInWithEmailAndPassword, 
  signOut, 
  onAuthStateChanged,
  updateProfile,
  sendPasswordResetEmail
} from 'firebase/auth';
import { doc, setDoc, getDoc } from 'firebase/firestore';
import { auth, db } from './config.js';

/**
 * Register a new user with email and password
 */
export const registerUser = async (username, email, password, isStoryteller = false) => {
  try {
    // Create user with Firebase Auth
    const userCredential = await createUserWithEmailAndPassword(auth, email, password);
    const user = userCredential.user;

    // Update the user's display name
    await updateProfile(user, {
      displayName: username
    });

    // Create user document in Firestore
    const userData = {
      id: user.uid,
      username: username,
      email: email,
      userRole: isStoryteller ? 'STORYTELLER' : 'PLAYER',
      gamesPlayed: 0,
      gamesWon: 0,
      gamesAsStoryteller: 0,
      createdAt: new Date().toISOString(),
      lastLoginAt: new Date().toISOString(),
      enabled: true
    };

    await setDoc(doc(db, 'users', user.uid), userData);

    return {
      success: true,
      user: {
        id: user.uid,
        username: username,
        email: email,
        userRole: isStoryteller ? 'STORYTELLER' : 'PLAYER',
        ...userData
      }
    };
  } catch (error) {
    console.error('Registration error:', error);
    return {
      success: false,
      message: getErrorMessage(error)
    };
  }
};

/**
 * Sign in user with email and password
 */
export const loginUser = async (username, email, password) => {
  try {
    // For this demo, we'll use email for login
    // In a real app, you might want to look up email by username
    const userCredential = await signInWithEmailAndPassword(auth, email, password);
    const user = userCredential.user;

    // Get user data from Firestore
    const userDoc = await getDoc(doc(db, 'users', user.uid));
    let userData = {};
    
    if (userDoc.exists()) {
      userData = userDoc.data();
      
      // Update last login time
      await setDoc(doc(db, 'users', user.uid), {
        ...userData,
        lastLoginAt: new Date().toISOString()
      }, { merge: true });
    }

    return {
      success: true,
      user: {
        id: user.uid,
        username: user.displayName || userData.username || username,
        email: user.email,
        userRole: userData.userRole || 'PLAYER',
        ...userData
      }
    };
  } catch (error) {
    console.error('Login error:', error);
    return {
      success: false,
      message: getErrorMessage(error)
    };
  }
};

/**
 * Sign out the current user
 */
export const logoutUser = async () => {
  try {
    await signOut(auth);
    return { success: true };
  } catch (error) {
    console.error('Logout error:', error);
    return {
      success: false,
      message: getErrorMessage(error)
    };
  }
};

/**
 * Get current user data
 */
export const getCurrentUser = async () => {
  const user = auth.currentUser;
  if (!user) return null;

  try {
    const userDoc = await getDoc(doc(db, 'users', user.uid));
    const userData = userDoc.exists() ? userDoc.data() : {};

    return {
      id: user.uid,
      username: user.displayName || userData.username,
      email: user.email,
      userRole: userData.userRole || 'PLAYER',
      ...userData
    };
  } catch (error) {
    console.error('Error getting current user:', error);
    return null;
  }
};

/**
 * Listen to authentication state changes
 */
export const onAuthStateChange = (callback) => {
  return onAuthStateChanged(auth, async (user) => {
    if (user) {
      // User is signed in
      try {
        const userDoc = await getDoc(doc(db, 'users', user.uid));
        const userData = userDoc.exists() ? userDoc.data() : {};

        callback({
          id: user.uid,
          username: user.displayName || userData.username,
          email: user.email,
          userRole: userData.userRole || 'PLAYER',
          ...userData
        });
      } catch (error) {
        console.error('Error getting user data:', error);
        callback(null);
      }
    } else {
      // User is signed out
      callback(null);
    }
  });
};

/**
 * Send password reset email
 */
export const resetPassword = async (email) => {
  try {
    await sendPasswordResetEmail(auth, email);
    return { success: true };
  } catch (error) {
    console.error('Password reset error:', error);
    return {
      success: false,
      message: getErrorMessage(error)
    };
  }
};

/**
 * Update user profile
 */
export const updateUserProfile = async (updates) => {
  const user = auth.currentUser;
  if (!user) throw new Error('No authenticated user');

  try {
    // Update Firebase Auth profile if needed
    if (updates.username && updates.username !== user.displayName) {
      await updateProfile(user, {
        displayName: updates.username
      });
    }

    // Update Firestore document
    await setDoc(doc(db, 'users', user.uid), updates, { merge: true });

    return { success: true };
  } catch (error) {
    console.error('Profile update error:', error);
    return {
      success: false,
      message: getErrorMessage(error)
    };
  }
};

/**
 * Get user ID token for API calls
 */
export const getUserToken = async () => {
  const user = auth.currentUser;
  if (!user) return null;

  try {
    return await user.getIdToken();
  } catch (error) {
    console.error('Error getting user token:', error);
    return null;
  }
};

/**
 * Convert Firebase error to user-friendly message
 */
const getErrorMessage = (error) => {
  switch (error.code) {
    case 'auth/user-not-found':
      return 'No user found with this email address.';
    case 'auth/wrong-password':
      return 'Incorrect password.';
    case 'auth/email-already-in-use':
      return 'An account with this email already exists.';
    case 'auth/weak-password':
      return 'Password should be at least 6 characters.';
    case 'auth/invalid-email':
      return 'Invalid email address.';
    case 'auth/too-many-requests':
      return 'Too many failed attempts. Please try again later.';
    default:
      return error.message || 'An error occurred. Please try again.';
  }
};
