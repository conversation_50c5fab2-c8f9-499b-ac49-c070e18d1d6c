import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { render, screen, act, waitFor, cleanup } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import GameTimer from './GameTimer'

// Mock timers
beforeEach(() => {
  vi.useFakeTimers()
})

afterEach(() => {
  vi.runOnlyPendingTimers()
  vi.useRealTimers()
  cleanup()
})

describe('GameTimer Component', () => {
  it('renders with default props', () => {
    render(<GameTimer />)

    expect(screen.getByTestId('game-timer')).toBeInTheDocument()
    expect(screen.getByText('Jour')).toBeInTheDocument()
    expect(screen.getByTestId('time-display')).toHaveTextContent('05:00')
    expect(screen.getByText('En pause')).toBeInTheDocument()
  })

  it('renders night phase correctly', () => {
    render(<GameTimer phase="night" />)

    expect(screen.getByText('Nuit')).toBeInTheDocument()
    expect(screen.getByTestId('game-timer')).toHaveClass('night')
  })

  it('displays custom initial time', () => {
    render(<GameTimer initialTime={120} />)

    expect(screen.getByTestId('time-display')).toHaveTextContent('02:00')
  })

  it('starts running when isRunning prop is true', () => {
    render(<GameTimer isRunning={true} />)

    expect(screen.getByText('En cours')).toBeInTheDocument()
  })

  it('formats time correctly', () => {
    const { rerender } = render(<GameTimer initialTime={65} />)
    expect(screen.getByTestId('time-display')).toHaveTextContent('01:05')

    rerender(<GameTimer initialTime={9} />)
    expect(screen.getByTestId('time-display')).toHaveTextContent('00:09')
  })

  it('starts timer when start button is clicked', async () => {
    const user = userEvent.setup({ advanceTimers: vi.advanceTimersByTime })
    render(<GameTimer initialTime={10} />)

    const startButton = screen.getByLabelText('Démarrer le minuteur')
    await user.click(startButton)

    expect(screen.getByText('En cours')).toBeInTheDocument()

    // Advance time by 1 second
    act(() => {
      vi.advanceTimersByTime(1000)
    })

    expect(screen.getByTestId('time-display')).toHaveTextContent('00:09')
  })

  it('pauses timer when pause button is clicked', async () => {
    const user = userEvent.setup({ advanceTimers: vi.advanceTimersByTime })
    render(<GameTimer initialTime={10} isRunning={true} />)

    const pauseButton = screen.getByLabelText('Mettre en pause le minuteur')
    await user.click(pauseButton)

    expect(screen.getByText('En pause')).toBeInTheDocument()

    // Advance time - should not change since paused
    act(() => {
      vi.advanceTimersByTime(1000)
    })

    expect(screen.getByTestId('time-display')).toHaveTextContent('00:10')
  })

  it('resets timer when reset button is clicked', async () => {
    const user = userEvent.setup({ advanceTimers: vi.advanceTimersByTime })
    render(<GameTimer initialTime={10} isRunning={true} />)

    // Let timer run for 2 seconds
    act(() => {
      vi.advanceTimersByTime(2000)
    })

    expect(screen.getByTestId('time-display')).toHaveTextContent('00:08')

    const resetButton = screen.getByLabelText('Remettre à zéro le minuteur')
    await user.click(resetButton)

    expect(screen.getByTestId('time-display')).toHaveTextContent('00:10')
    expect(screen.getByText('En pause')).toBeInTheDocument()
  })

  it('calls onTimeUp when timer reaches zero', async () => {
    const onTimeUp = vi.fn()
    render(<GameTimer initialTime={2} isRunning={true} onTimeUp={onTimeUp} />)

    // Advance time by 2 seconds to reach zero
    act(() => {
      vi.advanceTimersByTime(2000)
    })

    expect(onTimeUp).toHaveBeenCalledTimes(1)
    expect(screen.getByText('Terminé')).toBeInTheDocument()
    expect(screen.getByTestId('time-display')).toHaveTextContent('00:00')
  })

  it('calls onTick callback on each second', async () => {
    const onTick = vi.fn()
    render(<GameTimer initialTime={3} isRunning={true} onTick={onTick} />)

    // Advance time by 2 seconds
    act(() => {
      vi.advanceTimersByTime(2000)
    })

    expect(onTick).toHaveBeenCalledTimes(2)
    expect(onTick).toHaveBeenNthCalledWith(1, 2)
    expect(onTick).toHaveBeenNthCalledWith(2, 1)
  })

  it('disables start button when timer is running', () => {
    render(<GameTimer isRunning={true} />)

    const startButton = screen.getByLabelText('Démarrer le minuteur')
    expect(startButton).toBeDisabled()
  })

  it('disables start button when timer is at zero', async () => {
    const user = userEvent.setup({ advanceTimers: vi.advanceTimersByTime })
    render(<GameTimer initialTime={1} isRunning={true} />)

    // Let timer reach zero
    act(() => {
      vi.advanceTimersByTime(1000)
    })

    const startButton = screen.getByLabelText('Démarrer le minuteur')
    expect(startButton).toBeDisabled()
  })

  it('disables pause button when timer is not running', () => {
    render(<GameTimer isRunning={false} />)

    const pauseButton = screen.getByLabelText('Mettre en pause le minuteur')
    expect(pauseButton).toBeDisabled()
  })

  it('updates when initialTime prop changes', () => {
    const { rerender } = render(<GameTimer initialTime={60} />)
    expect(screen.getByTestId('time-display')).toHaveTextContent('01:00')

    rerender(<GameTimer initialTime={120} />)
    expect(screen.getByTestId('time-display')).toHaveTextContent('02:00')
  })

  it('updates when isRunning prop changes', () => {
    const { rerender } = render(<GameTimer isRunning={false} />)
    expect(screen.getByText('En pause')).toBeInTheDocument()

    rerender(<GameTimer isRunning={true} />)
    expect(screen.getByText('En cours')).toBeInTheDocument()
  })

  it('has proper accessibility attributes', () => {
    render(<GameTimer />)

    expect(screen.getByLabelText('Démarrer le minuteur')).toBeInTheDocument()
    expect(screen.getByLabelText('Mettre en pause le minuteur')).toBeInTheDocument()
    expect(screen.getByLabelText('Remettre à zéro le minuteur')).toBeInTheDocument()
  })
})
